#!/usr/bin/env node

/**
 * System Check Script for Arabic E-commerce Platform
 * This script checks if all prerequisites are installed and configured correctly
 */

const { spawn } = require('child_process');
const fs = require('fs');
const path = require('path');

// Colors for console output
const colors = {
  reset: '\x1b[0m',
  bright: '\x1b[1m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  cyan: '\x1b[36m'
};

const log = (message, color = 'reset') => {
  console.log(`${colors[color]}${message}${colors.reset}`);
};

// Check if command exists
function commandExists(command) {
  return new Promise((resolve) => {
    const child = spawn(command, ['--version'], { 
      stdio: 'pipe',
      shell: true 
    });
    
    child.on('close', (code) => {
      resolve(code === 0);
    });
    
    child.on('error', () => {
      resolve(false);
    });
  });
}

// Get command version
function getVersion(command, args = ['--version']) {
  return new Promise((resolve) => {
    const child = spawn(command, args, { 
      stdio: 'pipe',
      shell: true 
    });
    
    let output = '';
    child.stdout.on('data', (data) => {
      output += data.toString();
    });
    
    child.on('close', (code) => {
      if (code === 0) {
        resolve(output.trim().split('\n')[0]);
      } else {
        resolve(null);
      }
    });
    
    child.on('error', () => {
      resolve(null);
    });
  });
}

// Check MongoDB connection
function checkMongoDB() {
  return new Promise((resolve) => {
    const mongoose = require('mongoose');
    
    const mongoUri = process.env.MONGODB_URI || 'mongodb://localhost:27017/test';
    
    mongoose.connect(mongoUri, {
      useNewUrlParser: true,
      useUnifiedTopology: true,
      serverSelectionTimeoutMS: 3000
    });
    
    mongoose.connection.on('connected', () => {
      mongoose.connection.close();
      resolve(true);
    });
    
    mongoose.connection.on('error', () => {
      resolve(false);
    });
    
    setTimeout(() => {
      mongoose.connection.close();
      resolve(false);
    }, 5000);
  });
}

// Main check function
async function checkSystem() {
  log('\n🔍 فحص النظام - Arabic E-commerce Platform\n', 'cyan');
  
  let allGood = true;
  
  // Check Node.js
  log('📦 فحص Node.js...', 'blue');
  const nodeExists = await commandExists('node');
  if (nodeExists) {
    const nodeVersion = await getVersion('node');
    log(`✅ Node.js مثبت: ${nodeVersion}`, 'green');
    
    // Check version
    const majorVersion = parseInt(nodeVersion.replace('v', '').split('.')[0]);
    if (majorVersion < 16) {
      log(`⚠️  يُنصح بالإصدار 16 أو أحدث (الحالي: ${majorVersion})`, 'yellow');
    }
  } else {
    log('❌ Node.js غير مثبت', 'red');
    log('   📥 حمل من: https://nodejs.org/', 'yellow');
    allGood = false;
  }
  
  // Check npm
  log('\n📦 فحص npm...', 'blue');
  const npmExists = await commandExists('npm');
  if (npmExists) {
    const npmVersion = await getVersion('npm');
    log(`✅ npm مثبت: ${npmVersion}`, 'green');
  } else {
    log('❌ npm غير متاح', 'red');
    allGood = false;
  }
  
  // Check MongoDB
  log('\n🗄️  فحص MongoDB...', 'blue');
  const mongodExists = await commandExists('mongod');
  if (mongodExists) {
    const mongoVersion = await getVersion('mongod');
    log(`✅ MongoDB مثبت: ${mongoVersion}`, 'green');
  } else {
    log('⚠️  MongoDB غير مثبت محلياً', 'yellow');
    log('   📥 حمل من: https://www.mongodb.com/try/download/community', 'yellow');
    log('   🌐 أو استخدم MongoDB Atlas: https://www.mongodb.com/atlas', 'yellow');
  }
  
  // Check project files
  log('\n📁 فحص ملفات المشروع...', 'blue');
  
  const requiredFiles = [
    'package.json',
    'server.js',
    '.env.example'
  ];
  
  for (const file of requiredFiles) {
    if (fs.existsSync(file)) {
      log(`✅ ${file} موجود`, 'green');
    } else {
      log(`❌ ${file} مفقود`, 'red');
      allGood = false;
    }
  }
  
  // Check .env file
  if (fs.existsSync('.env')) {
    log('✅ .env موجود', 'green');
  } else {
    log('⚠️  .env غير موجود (سيتم إنشاؤه تلقائياً)', 'yellow');
  }
  
  // Check node_modules
  if (fs.existsSync('node_modules')) {
    log('✅ node_modules موجود', 'green');
  } else {
    log('⚠️  node_modules غير موجود (شغل npm install)', 'yellow');
  }
  
  // Check directories
  log('\n📂 فحص المجلدات...', 'blue');
  const requiredDirs = [
    'backend',
    'frontend',
    'public'
  ];
  
  for (const dir of requiredDirs) {
    if (fs.existsSync(dir)) {
      log(`✅ ${dir}/ موجود`, 'green');
    } else {
      log(`❌ ${dir}/ مفقود`, 'red');
      allGood = false;
    }
  }
  
  // Check Git (optional)
  log('\n🔧 فحص الأدوات الإضافية...', 'blue');
  const gitExists = await commandExists('git');
  if (gitExists) {
    const gitVersion = await getVersion('git');
    log(`✅ Git مثبت: ${gitVersion}`, 'green');
  } else {
    log('⚠️  Git غير مثبت (اختياري)', 'yellow');
  }
  
  // Summary
  log('\n📋 ملخص الفحص:', 'cyan');
  if (allGood) {
    log('🎉 جميع المتطلبات الأساسية متوفرة!', 'green');
    log('\n🚀 يمكنك الآن تشغيل المشروع:', 'cyan');
    log('   npm install', 'white');
    log('   npm run seed', 'white');
    log('   npm start', 'white');
    log('\n🌐 أو استخدم الإعداد السريع:', 'cyan');
    log('   npm run setup', 'white');
  } else {
    log('⚠️  بعض المتطلبات مفقودة', 'yellow');
    log('📖 راجع دليل الإعداد: SETUP_GUIDE.md', 'cyan');
  }
  
  log('\n📞 للمساعدة:', 'cyan');
  log('   📖 README.md', 'white');
  log('   📋 SETUP_GUIDE.md', 'white');
  log('   🌐 GitHub Issues', 'white');
  
  console.log('');
}

// Run the check
if (require.main === module) {
  checkSystem().catch(console.error);
}

module.exports = { checkSystem };
