const Product = require('../models/Product');
const Category = require('../models/Category');
const { AppError, asyncHandler } = require('../middleware/errorHandler');

// @desc    Get all products
// @route   GET /api/products
// @access  Public
exports.getProducts = asyncHandler(async (req, res, next) => {
  // Copy req.query
  const reqQuery = { ...req.query };

  // Fields to exclude
  const removeFields = ['select', 'sort', 'page', 'limit'];

  // Loop over removeFields and delete them from reqQuery
  removeFields.forEach(param => delete reqQuery[param]);

  // Create query string
  let queryStr = JSON.stringify(reqQuery);

  // Create operators ($gt, $gte, etc)
  queryStr = queryStr.replace(/\b(gt|gte|lt|lte|in)\b/g, match => `$${match}`);

  // Build query
  let query = Product.find(JSON.parse(queryStr));

  // Add default filters
  if (!req.user || req.user.role !== 'admin') {
    query = query.find({ status: 'active' });
  }

  // Select Fields
  if (req.query.select) {
    const fields = req.query.select.split(',').join(' ');
    query = query.select(fields);
  }

  // Sort
  if (req.query.sort) {
    const sortBy = req.query.sort.split(',').join(' ');
    query = query.sort(sortBy);
  } else {
    query = query.sort('-createdAt');
  }

  // Pagination
  const page = parseInt(req.query.page, 10) || 1;
  const limit = parseInt(req.query.limit, 10) || 20;
  const startIndex = (page - 1) * limit;
  const endIndex = page * limit;
  const total = await Product.countDocuments(JSON.parse(queryStr));

  query = query.skip(startIndex).limit(limit);

  // Populate
  query = query.populate('category', 'name slug');

  // Executing query
  const products = await query;

  // Pagination result
  const pagination = {};

  if (endIndex < total) {
    pagination.next = {
      page: page + 1,
      limit
    };
  }

  if (startIndex > 0) {
    pagination.prev = {
      page: page - 1,
      limit
    };
  }

  res.status(200).json({
    success: true,
    count: products.length,
    total,
    pagination,
    data: products
  });
});

// @desc    Get single product
// @route   GET /api/products/:id
// @access  Public
exports.getProduct = asyncHandler(async (req, res, next) => {
  const product = await Product.findById(req.params.id)
    .populate('category', 'name slug')
    .populate('reviews', 'rating comment user createdAt')
    .populate({
      path: 'reviews',
      populate: {
        path: 'user',
        select: 'name avatar'
      }
    });

  if (!product) {
    return next(new AppError(
      'المنتج غير موجود',
      404,
      'Product not found'
    ));
  }

  // Check if product is active (unless admin)
  if (product.status !== 'active' && (!req.user || req.user.role !== 'admin')) {
    return next(new AppError(
      'المنتج غير متاح',
      404,
      'Product not available'
    ));
  }

  res.status(200).json({
    success: true,
    data: product
  });
});

// @desc    Create new product
// @route   POST /api/products
// @access  Private (Admin/Manager)
exports.createProduct = asyncHandler(async (req, res, next) => {
  // Add user to req.body
  req.body.createdBy = req.user.id;

  // Check if category exists
  if (req.body.category) {
    const category = await Category.findById(req.body.category);
    if (!category) {
      return next(new AppError(
        'الفئة غير موجودة',
        404,
        'Category not found'
      ));
    }
  }

  // Handle uploaded images
  if (req.uploadedImages) {
    req.body.images = req.uploadedImages.map((img, index) => ({
      url: img.url,
      alt: {
        ar: req.body.name?.ar || 'صورة المنتج',
        en: req.body.name?.en || 'Product image'
      },
      isMain: index === 0
    }));
  }

  const product = await Product.create(req.body);

  // Update category product count
  if (product.category) {
    const category = await Category.findById(product.category);
    if (category) {
      await category.updateProductCount();
    }
  }

  res.status(201).json({
    success: true,
    message: 'تم إنشاء المنتج بنجاح',
    messageEn: 'Product created successfully',
    data: product
  });
});

// @desc    Update product
// @route   PUT /api/products/:id
// @access  Private (Admin/Manager)
exports.updateProduct = asyncHandler(async (req, res, next) => {
  let product = await Product.findById(req.params.id);

  if (!product) {
    return next(new AppError(
      'المنتج غير موجود',
      404,
      'Product not found'
    ));
  }

  // Check if category exists
  if (req.body.category && req.body.category !== product.category.toString()) {
    const category = await Category.findById(req.body.category);
    if (!category) {
      return next(new AppError(
        'الفئة غير موجودة',
        404,
        'Category not found'
      ));
    }
  }

  // Add user to req.body
  req.body.updatedBy = req.user.id;

  // Handle uploaded images
  if (req.uploadedImages) {
    const newImages = req.uploadedImages.map((img, index) => ({
      url: img.url,
      alt: {
        ar: req.body.name?.ar || product.name.ar,
        en: req.body.name?.en || product.name.en
      },
      isMain: index === 0 && (!product.images || product.images.length === 0)
    }));

    // Merge with existing images or replace
    if (req.body.replaceImages === 'true') {
      req.body.images = newImages;
    } else {
      req.body.images = [...(product.images || []), ...newImages];
    }
  }

  const oldCategory = product.category;
  product = await Product.findByIdAndUpdate(req.params.id, req.body, {
    new: true,
    runValidators: true
  });

  // Update category product counts if category changed
  if (oldCategory && oldCategory.toString() !== product.category.toString()) {
    const oldCat = await Category.findById(oldCategory);
    if (oldCat) await oldCat.updateProductCount();
    
    const newCat = await Category.findById(product.category);
    if (newCat) await newCat.updateProductCount();
  }

  res.status(200).json({
    success: true,
    message: 'تم تحديث المنتج بنجاح',
    messageEn: 'Product updated successfully',
    data: product
  });
});

// @desc    Delete product
// @route   DELETE /api/products/:id
// @access  Private (Admin)
exports.deleteProduct = asyncHandler(async (req, res, next) => {
  const product = await Product.findById(req.params.id);

  if (!product) {
    return next(new AppError(
      'المنتج غير موجود',
      404,
      'Product not found'
    ));
  }

  const categoryId = product.category;
  await product.remove();

  // Update category product count
  if (categoryId) {
    const category = await Category.findById(categoryId);
    if (category) {
      await category.updateProductCount();
    }
  }

  res.status(200).json({
    success: true,
    message: 'تم حذف المنتج بنجاح',
    messageEn: 'Product deleted successfully'
  });
});

// @desc    Get products by category
// @route   GET /api/products/category/:categoryId
// @access  Public
exports.getProductsByCategory = asyncHandler(async (req, res, next) => {
  const category = await Category.findById(req.params.categoryId);

  if (!category) {
    return next(new AppError(
      'الفئة غير موجودة',
      404,
      'Category not found'
    ));
  }

  // Get all descendant categories
  const descendants = await Category.getDescendants(req.params.categoryId);
  const categoryIds = [req.params.categoryId, ...descendants];

  // Build query
  let query = Product.find({
    category: { $in: categoryIds },
    status: 'active'
  });

  // Apply filters
  if (req.query.minPrice) {
    query = query.where('price').gte(req.query.minPrice);
  }
  if (req.query.maxPrice) {
    query = query.where('price').lte(req.query.maxPrice);
  }
  if (req.query.brand) {
    query = query.where('brand').equals(req.query.brand);
  }
  if (req.query.inStock === 'true') {
    query = query.where('stock.quantity').gt(0);
  }

  // Sort
  if (req.query.sort) {
    const sortBy = req.query.sort.split(',').join(' ');
    query = query.sort(sortBy);
  } else {
    query = query.sort('-createdAt');
  }

  // Pagination
  const page = parseInt(req.query.page, 10) || 1;
  const limit = parseInt(req.query.limit, 10) || 20;
  const startIndex = (page - 1) * limit;

  const total = await Product.countDocuments(query.getQuery());
  query = query.skip(startIndex).limit(limit);

  // Populate
  query = query.populate('category', 'name slug');

  const products = await query;

  res.status(200).json({
    success: true,
    count: products.length,
    total,
    category: {
      id: category._id,
      name: category.name,
      slug: category.slug
    },
    data: products
  });
});

// @desc    Search products
// @route   GET /api/products/search
// @access  Public
exports.searchProducts = asyncHandler(async (req, res, next) => {
  const { q, category, minPrice, maxPrice, brand, sort } = req.query;

  if (!q) {
    return next(new AppError(
      'كلمة البحث مطلوبة',
      400,
      'Search query is required'
    ));
  }

  // Build search query
  let searchQuery = {
    $text: { $search: q },
    status: 'active'
  };

  // Add filters
  if (category) {
    searchQuery.category = category;
  }
  if (minPrice) {
    searchQuery.price = { ...searchQuery.price, $gte: parseFloat(minPrice) };
  }
  if (maxPrice) {
    searchQuery.price = { ...searchQuery.price, $lte: parseFloat(maxPrice) };
  }
  if (brand) {
    searchQuery.brand = new RegExp(brand, 'i');
  }

  // Build query
  let query = Product.find(searchQuery, { score: { $meta: 'textScore' } });

  // Sort
  if (sort) {
    query = query.sort(sort);
  } else {
    query = query.sort({ score: { $meta: 'textScore' } });
  }

  // Pagination
  const page = parseInt(req.query.page, 10) || 1;
  const limit = parseInt(req.query.limit, 10) || 20;
  const startIndex = (page - 1) * limit;

  const total = await Product.countDocuments(searchQuery);
  query = query.skip(startIndex).limit(limit);

  // Populate
  query = query.populate('category', 'name slug');

  const products = await query;

  res.status(200).json({
    success: true,
    count: products.length,
    total,
    searchQuery: q,
    data: products
  });
});

// @desc    Get featured products
// @route   GET /api/products/featured
// @access  Public
exports.getFeaturedProducts = asyncHandler(async (req, res, next) => {
  const limit = parseInt(req.query.limit, 10) || 10;
  
  const products = await Product.getFeatured(limit);

  res.status(200).json({
    success: true,
    count: products.length,
    data: products
  });
});

// @desc    Get related products
// @route   GET /api/products/:id/related
// @access  Public
exports.getRelatedProducts = asyncHandler(async (req, res, next) => {
  const product = await Product.findById(req.params.id);

  if (!product) {
    return next(new AppError(
      'المنتج غير موجود',
      404,
      'Product not found'
    ));
  }

  const limit = parseInt(req.query.limit, 10) || 6;

  // Find related products in the same category
  const relatedProducts = await Product.find({
    category: product.category,
    _id: { $ne: product._id },
    status: 'active'
  })
    .populate('category', 'name slug')
    .sort({ 'rating.average': -1, 'sales.totalSold': -1 })
    .limit(limit);

  res.status(200).json({
    success: true,
    count: relatedProducts.length,
    data: relatedProducts
  });
});
