#!/usr/bin/env node

/**
 * Arabic E-commerce Platform
 * Main Application Entry Point
 * 
 * This file initializes and starts the e-commerce platform with proper
 * error handling, graceful shutdown, and comprehensive logging.
 */

const cluster = require('cluster');
const os = require('os');
const path = require('path');
const fs = require('fs');

// Load environment variables
require('dotenv').config();

// Colors for console output
const colors = {
  reset: '\x1b[0m',
  bright: '\x1b[1m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  magenta: '\x1b[35m',
  cyan: '\x1b[36m'
};

const log = (message, color = 'reset') => {
  const timestamp = new Date().toISOString();
  console.log(`${colors[color]}[${timestamp}] ${message}${colors.reset}`);
};

// Application configuration
const config = {
  port: process.env.PORT || 3000,
  host: process.env.HOST || 'localhost',
  nodeEnv: process.env.NODE_ENV || 'development',
  workers: process.env.WORKERS || os.cpus().length,
  enableClustering: process.env.ENABLE_CLUSTERING === 'true',
  mongoUri: process.env.MONGODB_URI || 'mongodb://localhost:27017/arabic_ecommerce'
};

// Validate required environment variables
const requiredEnvVars = [
  'MONGODB_URI',
  'JWT_SECRET',
  'EMAIL_HOST',
  'EMAIL_USER',
  'EMAIL_PASS'
];

function validateEnvironment() {
  const missing = requiredEnvVars.filter(envVar => !process.env[envVar]);
  
  if (missing.length > 0) {
    log(`❌ Missing required environment variables: ${missing.join(', ')}`, 'red');
    log('💡 Please check your .env file and ensure all required variables are set', 'yellow');
    process.exit(1);
  }
  
  log('✅ Environment variables validated', 'green');
}

// Create necessary directories
function createDirectories() {
  const directories = [
    'public/uploads',
    'public/uploads/products',
    'public/uploads/categories',
    'public/uploads/avatars',
    'public/uploads/general',
    'logs'
  ];

  directories.forEach(dir => {
    const fullPath = path.join(__dirname, dir);
    if (!fs.existsSync(fullPath)) {
      fs.mkdirSync(fullPath, { recursive: true });
      log(`📁 Created directory: ${dir}`, 'cyan');
    }
  });
}

// Display startup banner
function displayBanner() {
  const banner = `
${colors.cyan}
╔══════════════════════════════════════════════════════════════╗
║                                                              ║
║           🛍️  Arabic E-commerce Platform 🛍️                  ║
║                                                              ║
║              Professional E-commerce Solution                ║
║                   Built with Node.js & MongoDB              ║
║                                                              ║
╚══════════════════════════════════════════════════════════════╝
${colors.reset}`;

  console.log(banner);
  
  log('🚀 Starting Arabic E-commerce Platform...', 'bright');
  log(`📊 Environment: ${config.nodeEnv}`, 'cyan');
  log(`🌐 Host: ${config.host}`, 'cyan');
  log(`🔌 Port: ${config.port}`, 'cyan');
  log(`🗄️  Database: ${config.mongoUri.replace(/\/\/.*@/, '//***:***@')}`, 'cyan');
  
  if (config.enableClustering) {
    log(`👥 Workers: ${config.workers}`, 'cyan');
  }
  
  console.log('');
}

// Start worker process
function startWorker() {
  try {
    // Import and start the server
    const server = require('./server');
    
    log(`👷 Worker ${process.pid} started`, 'green');
    
    // Graceful shutdown for worker
    process.on('SIGTERM', () => {
      log(`👷 Worker ${process.pid} received SIGTERM, shutting down gracefully...`, 'yellow');
      server.close(() => {
        log(`👷 Worker ${process.pid} shut down`, 'green');
        process.exit(0);
      });
    });
    
  } catch (error) {
    log(`❌ Worker ${process.pid} failed to start: ${error.message}`, 'red');
    process.exit(1);
  }
}

// Start master process
function startMaster() {
  log('👑 Master process started', 'green');
  
  // Fork workers
  for (let i = 0; i < config.workers; i++) {
    cluster.fork();
  }
  
  // Handle worker events
  cluster.on('exit', (worker, code, signal) => {
    log(`👷 Worker ${worker.process.pid} died (${signal || code}). Restarting...`, 'yellow');
    cluster.fork();
  });
  
  cluster.on('online', (worker) => {
    log(`👷 Worker ${worker.process.pid} is online`, 'green');
  });
  
  // Graceful shutdown for master
  process.on('SIGTERM', () => {
    log('👑 Master received SIGTERM, shutting down workers...', 'yellow');
    
    for (const id in cluster.workers) {
      cluster.workers[id].kill();
    }
  });
  
  process.on('SIGINT', () => {
    log('👑 Master received SIGINT, shutting down workers...', 'yellow');
    
    for (const id in cluster.workers) {
      cluster.workers[id].kill();
    }
    
    setTimeout(() => {
      log('👑 Force shutdown', 'red');
      process.exit(1);
    }, 10000);
  });
}

// Health check endpoint for monitoring
function setupHealthCheck() {
  const express = require('express');
  const app = express();
  
  app.get('/health', (req, res) => {
    res.status(200).json({
      status: 'healthy',
      timestamp: new Date().toISOString(),
      uptime: process.uptime(),
      memory: process.memoryUsage(),
      pid: process.pid
    });
  });
  
  return app;
}

// Performance monitoring
function setupPerformanceMonitoring() {
  if (config.nodeEnv === 'production') {
    // Monitor memory usage
    setInterval(() => {
      const memUsage = process.memoryUsage();
      const memUsageMB = {
        rss: Math.round(memUsage.rss / 1024 / 1024),
        heapTotal: Math.round(memUsage.heapTotal / 1024 / 1024),
        heapUsed: Math.round(memUsage.heapUsed / 1024 / 1024),
        external: Math.round(memUsage.external / 1024 / 1024)
      };
      
      // Log if memory usage is high
      if (memUsageMB.heapUsed > 500) {
        log(`⚠️  High memory usage: ${JSON.stringify(memUsageMB)}`, 'yellow');
      }
    }, 60000); // Check every minute
  }
}

// Main application startup
async function main() {
  try {
    // Display banner
    displayBanner();
    
    // Validate environment
    validateEnvironment();
    
    // Create necessary directories
    createDirectories();
    
    // Setup performance monitoring
    setupPerformanceMonitoring();
    
    // Start application
    if (config.enableClustering && cluster.isMaster) {
      startMaster();
    } else {
      startWorker();
    }
    
  } catch (error) {
    log(`❌ Application startup failed: ${error.message}`, 'red');
    console.error(error);
    process.exit(1);
  }
}

// Handle uncaught exceptions
process.on('uncaughtException', (error) => {
  log(`❌ Uncaught Exception: ${error.message}`, 'red');
  console.error(error);
  process.exit(1);
});

// Handle unhandled promise rejections
process.on('unhandledRejection', (reason, promise) => {
  log(`❌ Unhandled Rejection at: ${promise}, reason: ${reason}`, 'red');
  console.error(reason);
  process.exit(1);
});

// Start the application
if (require.main === module) {
  main();
}

module.exports = {
  config,
  main,
  startWorker,
  startMaster
};
