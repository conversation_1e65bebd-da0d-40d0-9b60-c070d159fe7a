const mongoose = require('mongoose');
const bcrypt = require('bcryptjs');
require('dotenv').config();

// Import models
const User = require('../backend/models/User');
const Category = require('../backend/models/Category');
const Product = require('../backend/models/Product');
const Coupon = require('../backend/models/Coupon');

// Connect to MongoDB
const connectDB = async () => {
  try {
    await mongoose.connect(process.env.MONGODB_URI, {
      useNewUrlParser: true,
      useUnifiedTopology: true,
    });
    console.log('🗄️  MongoDB Connected for seeding');
  } catch (error) {
    console.error('❌ Database connection failed:', error.message);
    process.exit(1);
  }
};

// Sample data
const sampleUsers = [
  {
    name: 'مدير النظام',
    email: '<EMAIL>',
    password: 'admin123456',
    phone: '+966501234567',
    role: 'admin',
    isActive: true,
    isEmailVerified: true
  },
  {
    name: 'أحمد محمد',
    email: '<EMAIL>',
    password: 'user123456',
    phone: '+966501234568',
    role: 'user',
    isActive: true,
    isEmailVerified: true
  },
  {
    name: 'فاطمة علي',
    email: '<EMAIL>',
    password: 'user123456',
    phone: '+966501234569',
    role: 'user',
    isActive: true,
    isEmailVerified: true
  }
];

const sampleCategories = [
  {
    name: { ar: 'الإلكترونيات', en: 'Electronics' },
    description: { ar: 'أجهزة إلكترونية متنوعة', en: 'Various electronic devices' },
    status: 'active',
    featured: true,
    sortOrder: 1,
    icon: 'fas fa-laptop'
  },
  {
    name: { ar: 'الأزياء', en: 'Fashion' },
    description: { ar: 'ملابس وأزياء عصرية', en: 'Modern clothes and fashion' },
    status: 'active',
    featured: true,
    sortOrder: 2,
    icon: 'fas fa-tshirt'
  },
  {
    name: { ar: 'المنزل والحديقة', en: 'Home & Garden' },
    description: { ar: 'أدوات منزلية ومستلزمات الحديقة', en: 'Home tools and garden supplies' },
    status: 'active',
    featured: true,
    sortOrder: 3,
    icon: 'fas fa-home'
  },
  {
    name: { ar: 'الكتب', en: 'Books' },
    description: { ar: 'كتب متنوعة في جميع المجالات', en: 'Various books in all fields' },
    status: 'active',
    featured: false,
    sortOrder: 4,
    icon: 'fas fa-book'
  }
];

const sampleProducts = [
  {
    name: { ar: 'هاتف ذكي متطور', en: 'Advanced Smartphone' },
    description: { 
      ar: 'هاتف ذكي بمواصفات عالية وكاميرا متطورة وبطارية طويلة المدى',
      en: 'Smartphone with high specifications, advanced camera and long-lasting battery'
    },
    shortDescription: {
      ar: 'هاتف ذكي بمواصفات عالية',
      en: 'High-spec smartphone'
    },
    price: 2500,
    comparePrice: 3000,
    sku: 'PHONE001',
    brand: 'TechBrand',
    stock: { quantity: 50, lowStockThreshold: 5 },
    status: 'active',
    featured: true,
    tags: ['هاتف', 'ذكي', 'تقنية'],
    attributes: [
      { name: { ar: 'الذاكرة', en: 'Memory' }, value: { ar: '128 جيجا', en: '128GB' } },
      { name: { ar: 'الشاشة', en: 'Screen' }, value: { ar: '6.5 بوصة', en: '6.5 inch' } }
    ]
  },
  {
    name: { ar: 'قميص قطني أنيق', en: 'Elegant Cotton Shirt' },
    description: { 
      ar: 'قميص قطني عالي الجودة مناسب للمناسبات الرسمية والكاجوال',
      en: 'High-quality cotton shirt suitable for formal and casual occasions'
    },
    shortDescription: {
      ar: 'قميص قطني عالي الجودة',
      en: 'High-quality cotton shirt'
    },
    price: 150,
    comparePrice: 200,
    sku: 'SHIRT001',
    brand: 'FashionBrand',
    stock: { quantity: 100, lowStockThreshold: 10 },
    status: 'active',
    featured: true,
    tags: ['قميص', 'قطن', 'أزياء'],
    variants: [
      {
        name: { ar: 'المقاس', en: 'Size' },
        options: [
          { value: { ar: 'صغير', en: 'Small' }, price: 0, stock: 25 },
          { value: { ar: 'متوسط', en: 'Medium' }, price: 0, stock: 50 },
          { value: { ar: 'كبير', en: 'Large' }, price: 0, stock: 25 }
        ]
      }
    ]
  },
  {
    name: { ar: 'كتاب البرمجة الحديثة', en: 'Modern Programming Book' },
    description: { 
      ar: 'كتاب شامل يغطي أحدث تقنيات البرمجة والتطوير',
      en: 'Comprehensive book covering the latest programming and development techniques'
    },
    shortDescription: {
      ar: 'كتاب شامل للبرمجة الحديثة',
      en: 'Comprehensive modern programming book'
    },
    price: 80,
    comparePrice: 100,
    sku: 'BOOK001',
    brand: 'TechBooks',
    stock: { quantity: 30, lowStockThreshold: 5 },
    status: 'active',
    featured: false,
    tags: ['كتاب', 'برمجة', 'تعليم']
  },
  {
    name: { ar: 'مصباح LED ذكي', en: 'Smart LED Lamp' },
    description: { 
      ar: 'مصباح LED ذكي يمكن التحكم فيه عبر التطبيق مع إضاءة متغيرة الألوان',
      en: 'Smart LED lamp controllable via app with color-changing lighting'
    },
    shortDescription: {
      ar: 'مصباح LED ذكي متعدد الألوان',
      en: 'Smart multi-color LED lamp'
    },
    price: 120,
    comparePrice: 150,
    sku: 'LAMP001',
    brand: 'SmartHome',
    stock: { quantity: 75, lowStockThreshold: 10 },
    status: 'active',
    featured: true,
    tags: ['مصباح', 'ذكي', 'منزل']
  }
];

const sampleCoupons = [
  {
    code: 'WELCOME10',
    name: { ar: 'خصم الترحيب', en: 'Welcome Discount' },
    description: { ar: 'خصم 10% للعملاء الجدد', en: '10% discount for new customers' },
    type: 'percentage',
    value: 10,
    minimumAmount: 100,
    maximumDiscount: 50,
    usageLimit: { total: 100, perUser: 1 },
    validFrom: new Date(),
    validUntil: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000), // 30 days
    isActive: true,
    isPublic: true,
    conditions: { firstOrderOnly: true }
  },
  {
    code: 'SAVE20',
    name: { ar: 'وفر 20 ريال', en: 'Save 20 SAR' },
    description: { ar: 'خصم 20 ريال على الطلبات أكثر من 200 ريال', en: '20 SAR discount on orders over 200 SAR' },
    type: 'fixed',
    value: 20,
    minimumAmount: 200,
    usageLimit: { total: 50, perUser: 2 },
    validFrom: new Date(),
    validUntil: new Date(Date.now() + 15 * 24 * 60 * 60 * 1000), // 15 days
    isActive: true,
    isPublic: true
  }
];

// Seed functions
const seedUsers = async () => {
  try {
    await User.deleteMany({});
    console.log('🗑️  Cleared existing users');

    for (const userData of sampleUsers) {
      const user = new User(userData);
      await user.save();
    }

    console.log('👥 Sample users created');
  } catch (error) {
    console.error('❌ Error seeding users:', error);
  }
};

const seedCategories = async () => {
  try {
    await Category.deleteMany({});
    console.log('🗑️  Cleared existing categories');

    const createdCategories = [];
    for (const categoryData of sampleCategories) {
      const category = await Category.create(categoryData);
      createdCategories.push(category);
    }

    console.log('📂 Sample categories created');
    return createdCategories;
  } catch (error) {
    console.error('❌ Error seeding categories:', error);
    return [];
  }
};

const seedProducts = async (categories) => {
  try {
    await Product.deleteMany({});
    console.log('🗑️  Cleared existing products');

    const admin = await User.findOne({ role: 'admin' });
    
    for (let i = 0; i < sampleProducts.length; i++) {
      const productData = {
        ...sampleProducts[i],
        category: categories[i % categories.length]._id,
        createdBy: admin._id
      };
      
      await Product.create(productData);
    }

    console.log('📦 Sample products created');
  } catch (error) {
    console.error('❌ Error seeding products:', error);
  }
};

const seedCoupons = async () => {
  try {
    await Coupon.deleteMany({});
    console.log('🗑️  Cleared existing coupons');

    const admin = await User.findOne({ role: 'admin' });
    
    for (const couponData of sampleCoupons) {
      await Coupon.create({
        ...couponData,
        createdBy: admin._id
      });
    }

    console.log('🎫 Sample coupons created');
  } catch (error) {
    console.error('❌ Error seeding coupons:', error);
  }
};

// Main seed function
const seedDatabase = async () => {
  try {
    console.log('🌱 Starting database seeding...');
    
    await connectDB();
    
    await seedUsers();
    const categories = await seedCategories();
    await seedProducts(categories);
    await seedCoupons();
    
    console.log('✅ Database seeding completed successfully!');
    console.log('\n📋 Sample accounts created:');
    console.log('👤 Admin: <EMAIL> / admin123456');
    console.log('👤 User: <EMAIL> / user123456');
    console.log('👤 User: <EMAIL> / user123456');
    console.log('\n🎫 Sample coupons:');
    console.log('🏷️  WELCOME10 - 10% discount for new customers');
    console.log('🏷️  SAVE20 - 20 SAR discount on orders over 200 SAR');
    
    process.exit(0);
  } catch (error) {
    console.error('❌ Database seeding failed:', error);
    process.exit(1);
  }
};

// Run seeding if this file is executed directly
if (require.main === module) {
  seedDatabase();
}

module.exports = {
  seedDatabase,
  seedUsers,
  seedCategories,
  seedProducts,
  seedCoupons
};
