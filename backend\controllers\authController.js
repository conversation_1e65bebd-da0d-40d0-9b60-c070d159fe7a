const crypto = require('crypto');
const User = require('../models/User');
const { AppError, asyncHandler } = require('../middleware/errorHandler');
const sendEmail = require('../utils/sendEmail');

// Generate JWT token and set cookie
const sendTokenResponse = (user, statusCode, res, message = null, messageEn = null) => {
  // Create token
  const token = user.getSignedJwtToken();

  const options = {
    expires: new Date(
      Date.now() + process.env.JWT_COOKIE_EXPIRE * 24 * 60 * 60 * 1000
    ),
    httpOnly: true,
    secure: process.env.NODE_ENV === 'production',
    sameSite: 'strict'
  };

  // Remove password from output
  user.password = undefined;

  res
    .status(statusCode)
    .cookie('token', token, options)
    .json({
      success: true,
      message: message || 'تم بنجاح',
      messageEn: messageEn || 'Success',
      token,
      user
    });
};

// @desc    Register user
// @route   POST /api/auth/register
// @access  Public
exports.register = asyncHandler(async (req, res, next) => {
  const { name, email, password, phone } = req.body;

  // Check if user already exists
  const existingUser = await User.findOne({
    $or: [{ email }, { phone }]
  });

  if (existingUser) {
    if (existingUser.email === email) {
      return next(new AppError(
        'البريد الإلكتروني مستخدم بالفعل',
        400,
        'Email already exists'
      ));
    }
    if (existingUser.phone === phone) {
      return next(new AppError(
        'رقم الهاتف مستخدم بالفعل',
        400,
        'Phone number already exists'
      ));
    }
  }

  // Create user
  const user = await User.create({
    name,
    email,
    password,
    phone
  });

  // Generate email verification token
  const verificationToken = crypto.randomBytes(20).toString('hex');
  user.emailVerificationToken = crypto
    .createHash('sha256')
    .update(verificationToken)
    .digest('hex');
  user.emailVerificationExpire = Date.now() + 24 * 60 * 60 * 1000; // 24 hours

  await user.save({ validateBeforeSave: false });

  // Send verification email
  try {
    const verificationUrl = `${req.protocol}://${req.get('host')}/api/auth/verify-email/${verificationToken}`;
    
    await sendEmail({
      email: user.email,
      subject: 'تأكيد البريد الإلكتروني - Email Verification',
      template: 'emailVerification',
      data: {
        name: user.name,
        verificationUrl,
        language: 'ar'
      }
    });

    sendTokenResponse(
      user,
      201,
      res,
      'تم إنشاء الحساب بنجاح. يرجى تأكيد البريد الإلكتروني',
      'Account created successfully. Please verify your email'
    );
  } catch (error) {
    console.error('Email sending error:', error);
    
    // Remove verification token if email fails
    user.emailVerificationToken = undefined;
    user.emailVerificationExpire = undefined;
    await user.save({ validateBeforeSave: false });

    sendTokenResponse(
      user,
      201,
      res,
      'تم إنشاء الحساب بنجاح',
      'Account created successfully'
    );
  }
});

// @desc    Login user
// @route   POST /api/auth/login
// @access  Public
exports.login = asyncHandler(async (req, res, next) => {
  const { email, password } = req.body;

  // Find user and include password
  const user = await User.findOne({ email }).select('+password');

  if (!user) {
    return next(new AppError(
      'البريد الإلكتروني أو كلمة المرور غير صحيحة',
      401,
      'Invalid email or password'
    ));
  }

  // Check if account is locked
  if (user.isLocked) {
    return next(new AppError(
      'تم قفل حسابك مؤقتاً بسبب محاولات دخول خاطئة متكررة',
      401,
      'Account temporarily locked due to too many failed login attempts'
    ));
  }

  // Check if account is active
  if (!user.isActive) {
    return next(new AppError(
      'تم إيقاف حسابك، يرجى التواصل مع الإدارة',
      401,
      'Your account has been deactivated'
    ));
  }

  // Check if password matches
  const isMatch = await user.matchPassword(password);

  if (!isMatch) {
    // Increment login attempts
    await user.incLoginAttempts();
    
    return next(new AppError(
      'البريد الإلكتروني أو كلمة المرور غير صحيحة',
      401,
      'Invalid email or password'
    ));
  }

  // Reset login attempts on successful login
  if (user.loginAttempts > 0) {
    await user.resetLoginAttempts();
  }

  sendTokenResponse(
    user,
    200,
    res,
    'تم تسجيل الدخول بنجاح',
    'Login successful'
  );
});

// @desc    Logout user / clear cookie
// @route   GET /api/auth/logout
// @access  Private
exports.logout = asyncHandler(async (req, res, next) => {
  res.cookie('token', 'none', {
    expires: new Date(Date.now() + 10 * 1000),
    httpOnly: true
  });

  res.status(200).json({
    success: true,
    message: 'تم تسجيل الخروج بنجاح',
    messageEn: 'Logout successful'
  });
});

// @desc    Get current logged in user
// @route   GET /api/auth/me
// @access  Private
exports.getMe = asyncHandler(async (req, res, next) => {
  const user = await User.findById(req.user.id).populate('orders');

  res.status(200).json({
    success: true,
    data: user
  });
});

// @desc    Update user details
// @route   PUT /api/auth/updatedetails
// @access  Private
exports.updateDetails = asyncHandler(async (req, res, next) => {
  const fieldsToUpdate = {
    name: req.body.name,
    phone: req.body.phone
  };

  // Remove undefined fields
  Object.keys(fieldsToUpdate).forEach(key => 
    fieldsToUpdate[key] === undefined && delete fieldsToUpdate[key]
  );

  const user = await User.findByIdAndUpdate(req.user.id, fieldsToUpdate, {
    new: true,
    runValidators: true
  });

  res.status(200).json({
    success: true,
    message: 'تم تحديث البيانات بنجاح',
    messageEn: 'Details updated successfully',
    data: user
  });
});

// @desc    Update password
// @route   PUT /api/auth/updatepassword
// @access  Private
exports.updatePassword = asyncHandler(async (req, res, next) => {
  const user = await User.findById(req.user.id).select('+password');

  // Check current password
  if (!(await user.matchPassword(req.body.currentPassword))) {
    return next(new AppError(
      'كلمة المرور الحالية غير صحيحة',
      401,
      'Current password is incorrect'
    ));
  }

  user.password = req.body.newPassword;
  await user.save();

  sendTokenResponse(
    user,
    200,
    res,
    'تم تغيير كلمة المرور بنجاح',
    'Password updated successfully'
  );
});

// @desc    Forgot password
// @route   POST /api/auth/forgotpassword
// @access  Public
exports.forgotPassword = asyncHandler(async (req, res, next) => {
  const user = await User.findOne({ email: req.body.email });

  if (!user) {
    return next(new AppError(
      'لا يوجد مستخدم بهذا البريد الإلكتروني',
      404,
      'No user found with this email'
    ));
  }

  // Get reset token
  const resetToken = user.getResetPasswordToken();

  await user.save({ validateBeforeSave: false });

  // Create reset url
  const resetUrl = `${req.protocol}://${req.get('host')}/reset-password/${resetToken}`;

  try {
    await sendEmail({
      email: user.email,
      subject: 'استعادة كلمة المرور - Password Reset',
      template: 'passwordReset',
      data: {
        name: user.name,
        resetUrl,
        language: 'ar'
      }
    });

    res.status(200).json({
      success: true,
      message: 'تم إرسال رابط استعادة كلمة المرور إلى بريدك الإلكتروني',
      messageEn: 'Password reset link sent to your email'
    });
  } catch (error) {
    console.error('Email sending error:', error);
    
    user.resetPasswordToken = undefined;
    user.resetPasswordExpire = undefined;

    await user.save({ validateBeforeSave: false });

    return next(new AppError(
      'خطأ في إرسال البريد الإلكتروني',
      500,
      'Email could not be sent'
    ));
  }
});

// @desc    Reset password
// @route   PUT /api/auth/resetpassword/:resettoken
// @access  Public
exports.resetPassword = asyncHandler(async (req, res, next) => {
  // Get hashed token
  const resetPasswordToken = crypto
    .createHash('sha256')
    .update(req.params.resettoken)
    .digest('hex');

  const user = await User.findOne({
    resetPasswordToken,
    resetPasswordExpire: { $gt: Date.now() }
  });

  if (!user) {
    return next(new AppError(
      'رمز استعادة كلمة المرور غير صحيح أو منتهي الصلاحية',
      400,
      'Invalid or expired reset token'
    ));
  }

  // Set new password
  user.password = req.body.password;
  user.resetPasswordToken = undefined;
  user.resetPasswordExpire = undefined;

  await user.save();

  sendTokenResponse(
    user,
    200,
    res,
    'تم تغيير كلمة المرور بنجاح',
    'Password reset successful'
  );
});

// @desc    Verify email
// @route   GET /api/auth/verify-email/:token
// @access  Public
exports.verifyEmail = asyncHandler(async (req, res, next) => {
  // Get hashed token
  const emailVerificationToken = crypto
    .createHash('sha256')
    .update(req.params.token)
    .digest('hex');

  const user = await User.findOne({
    emailVerificationToken,
    emailVerificationExpire: { $gt: Date.now() }
  });

  if (!user) {
    return next(new AppError(
      'رمز تأكيد البريد الإلكتروني غير صحيح أو منتهي الصلاحية',
      400,
      'Invalid or expired verification token'
    ));
  }

  // Mark email as verified
  user.isEmailVerified = true;
  user.emailVerificationToken = undefined;
  user.emailVerificationExpire = undefined;

  await user.save({ validateBeforeSave: false });

  res.status(200).json({
    success: true,
    message: 'تم تأكيد البريد الإلكتروني بنجاح',
    messageEn: 'Email verified successfully'
  });
});

// @desc    Resend email verification
// @route   POST /api/auth/resend-verification
// @access  Private
exports.resendVerification = asyncHandler(async (req, res, next) => {
  const user = await User.findById(req.user.id);

  if (user.isEmailVerified) {
    return next(new AppError(
      'البريد الإلكتروني مؤكد بالفعل',
      400,
      'Email already verified'
    ));
  }

  // Generate new verification token
  const verificationToken = crypto.randomBytes(20).toString('hex');
  user.emailVerificationToken = crypto
    .createHash('sha256')
    .update(verificationToken)
    .digest('hex');
  user.emailVerificationExpire = Date.now() + 24 * 60 * 60 * 1000; // 24 hours

  await user.save({ validateBeforeSave: false });

  // Create verification url
  const verificationUrl = `${req.protocol}://${req.get('host')}/api/auth/verify-email/${verificationToken}`;

  try {
    await sendEmail({
      email: user.email,
      subject: 'تأكيد البريد الإلكتروني - Email Verification',
      template: 'emailVerification',
      data: {
        name: user.name,
        verificationUrl,
        language: 'ar'
      }
    });

    res.status(200).json({
      success: true,
      message: 'تم إرسال رابط تأكيد البريد الإلكتروني',
      messageEn: 'Verification email sent'
    });
  } catch (error) {
    console.error('Email sending error:', error);
    
    user.emailVerificationToken = undefined;
    user.emailVerificationExpire = undefined;
    await user.save({ validateBeforeSave: false });

    return next(new AppError(
      'خطأ في إرسال البريد الإلكتروني',
      500,
      'Email could not be sent'
    ));
  }
});
