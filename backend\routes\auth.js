const express = require('express');
const {
  register,
  login,
  logout,
  getMe,
  updateDetails,
  updatePassword,
  forgotPassword,
  resetPassword,
  verifyEmail,
  resendVerification
} = require('../controllers/authController');

const { protect, sensitiveOperationLimit } = require('../middleware/auth');
const {
  validateUserRegistration,
  validateUserLogin,
  validateUserUpdate,
  validatePasswordChange
} = require('../middleware/validation');

const router = express.Router();

/**
 * @swagger
 * components:
 *   schemas:
 *     User:
 *       type: object
 *       required:
 *         - name
 *         - email
 *         - password
 *         - phone
 *       properties:
 *         name:
 *           type: string
 *           description: اسم المستخدم
 *         email:
 *           type: string
 *           format: email
 *           description: البريد الإلكتروني
 *         password:
 *           type: string
 *           minLength: 6
 *           description: كلمة المرور
 *         phone:
 *           type: string
 *           description: رقم الهاتف
 *         role:
 *           type: string
 *           enum: [user, admin, manager, editor]
 *           description: دور المستخدم
 *         isActive:
 *           type: boolean
 *           description: حالة النشاط
 *         isEmailVerified:
 *           type: boolean
 *           description: حالة تأكيد البريد الإلكتروني
 */

/**
 * @swagger
 * /api/auth/register:
 *   post:
 *     summary: تسجيل مستخدم جديد
 *     tags: [Authentication]
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - name
 *               - email
 *               - password
 *               - phone
 *             properties:
 *               name:
 *                 type: string
 *                 example: أحمد محمد
 *               email:
 *                 type: string
 *                 format: email
 *                 example: <EMAIL>
 *               password:
 *                 type: string
 *                 minLength: 6
 *                 example: Password123
 *               phone:
 *                 type: string
 *                 example: +966501234567
 *     responses:
 *       201:
 *         description: تم إنشاء الحساب بنجاح
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                 message:
 *                   type: string
 *                 token:
 *                   type: string
 *                 user:
 *                   $ref: '#/components/schemas/User'
 *       400:
 *         description: خطأ في البيانات المدخلة
 */
router.post('/register', validateUserRegistration, register);

/**
 * @swagger
 * /api/auth/login:
 *   post:
 *     summary: تسجيل الدخول
 *     tags: [Authentication]
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - email
 *               - password
 *             properties:
 *               email:
 *                 type: string
 *                 format: email
 *                 example: <EMAIL>
 *               password:
 *                 type: string
 *                 example: Password123
 *     responses:
 *       200:
 *         description: تم تسجيل الدخول بنجاح
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                 message:
 *                   type: string
 *                 token:
 *                   type: string
 *                 user:
 *                   $ref: '#/components/schemas/User'
 *       401:
 *         description: بيانات الدخول غير صحيحة
 */
router.post('/login', validateUserLogin, sensitiveOperationLimit(5, 15 * 60 * 1000), login);

/**
 * @swagger
 * /api/auth/logout:
 *   get:
 *     summary: تسجيل الخروج
 *     tags: [Authentication]
 *     security:
 *       - bearerAuth: []
 *     responses:
 *       200:
 *         description: تم تسجيل الخروج بنجاح
 */
router.get('/logout', logout);

/**
 * @swagger
 * /api/auth/me:
 *   get:
 *     summary: الحصول على بيانات المستخدم الحالي
 *     tags: [Authentication]
 *     security:
 *       - bearerAuth: []
 *     responses:
 *       200:
 *         description: بيانات المستخدم
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                 data:
 *                   $ref: '#/components/schemas/User'
 *       401:
 *         description: غير مصرح
 */
router.get('/me', protect, getMe);

/**
 * @swagger
 * /api/auth/updatedetails:
 *   put:
 *     summary: تحديث بيانات المستخدم
 *     tags: [Authentication]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               name:
 *                 type: string
 *                 example: أحمد محمد علي
 *               phone:
 *                 type: string
 *                 example: +966501234567
 *     responses:
 *       200:
 *         description: تم تحديث البيانات بنجاح
 *       401:
 *         description: غير مصرح
 */
router.put('/updatedetails', protect, validateUserUpdate, updateDetails);

/**
 * @swagger
 * /api/auth/updatepassword:
 *   put:
 *     summary: تغيير كلمة المرور
 *     tags: [Authentication]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - currentPassword
 *               - newPassword
 *             properties:
 *               currentPassword:
 *                 type: string
 *                 example: OldPassword123
 *               newPassword:
 *                 type: string
 *                 minLength: 6
 *                 example: NewPassword123
 *     responses:
 *       200:
 *         description: تم تغيير كلمة المرور بنجاح
 *       401:
 *         description: كلمة المرور الحالية غير صحيحة
 */
router.put('/updatepassword', protect, validatePasswordChange, sensitiveOperationLimit(3, 15 * 60 * 1000), updatePassword);

/**
 * @swagger
 * /api/auth/forgotpassword:
 *   post:
 *     summary: طلب استعادة كلمة المرور
 *     tags: [Authentication]
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - email
 *             properties:
 *               email:
 *                 type: string
 *                 format: email
 *                 example: <EMAIL>
 *     responses:
 *       200:
 *         description: تم إرسال رابط استعادة كلمة المرور
 *       404:
 *         description: المستخدم غير موجود
 */
router.post('/forgotpassword', sensitiveOperationLimit(3, 15 * 60 * 1000), forgotPassword);

/**
 * @swagger
 * /api/auth/resetpassword/{resettoken}:
 *   put:
 *     summary: استعادة كلمة المرور
 *     tags: [Authentication]
 *     parameters:
 *       - in: path
 *         name: resettoken
 *         required: true
 *         schema:
 *           type: string
 *         description: رمز استعادة كلمة المرور
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - password
 *             properties:
 *               password:
 *                 type: string
 *                 minLength: 6
 *                 example: NewPassword123
 *     responses:
 *       200:
 *         description: تم تغيير كلمة المرور بنجاح
 *       400:
 *         description: رمز غير صحيح أو منتهي الصلاحية
 */
router.put('/resetpassword/:resettoken', resetPassword);

/**
 * @swagger
 * /api/auth/verify-email/{token}:
 *   get:
 *     summary: تأكيد البريد الإلكتروني
 *     tags: [Authentication]
 *     parameters:
 *       - in: path
 *         name: token
 *         required: true
 *         schema:
 *           type: string
 *         description: رمز تأكيد البريد الإلكتروني
 *     responses:
 *       200:
 *         description: تم تأكيد البريد الإلكتروني بنجاح
 *       400:
 *         description: رمز غير صحيح أو منتهي الصلاحية
 */
router.get('/verify-email/:token', verifyEmail);

/**
 * @swagger
 * /api/auth/resend-verification:
 *   post:
 *     summary: إعادة إرسال رابط تأكيد البريد الإلكتروني
 *     tags: [Authentication]
 *     security:
 *       - bearerAuth: []
 *     responses:
 *       200:
 *         description: تم إرسال رابط التأكيد
 *       400:
 *         description: البريد الإلكتروني مؤكد بالفعل
 */
router.post('/resend-verification', protect, sensitiveOperationLimit(3, 15 * 60 * 1000), resendVerification);

module.exports = router;
