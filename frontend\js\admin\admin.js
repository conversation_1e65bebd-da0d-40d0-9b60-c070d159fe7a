// Admin Panel Main JavaScript

// Admin Configuration
const AdminConfig = {
  API_BASE_URL: '/api',
  ITEMS_PER_PAGE: 20,
  CHART_COLORS: {
    primary: '#007bff',
    success: '#28a745',
    warning: '#ffc107',
    danger: '#dc3545',
    info: '#17a2b8',
    secondary: '#6c757d'
  }
};

// Admin State
const AdminState = {
  user: null,
  stats: {},
  charts: {},
  isLoading: false
};

// Admin API Service
const AdminAPI = {
  // Generic API call with admin authentication
  async call(endpoint, options = {}) {
    const url = `${AdminConfig.API_BASE_URL}${endpoint}`;
    const defaultOptions = {
      headers: {
        'Content-Type': 'application/json'
      }
    };

    // Add auth token
    const token = localStorage.getItem('token');
    if (token) {
      defaultOptions.headers.Authorization = `Bearer ${token}`;
    }

    const finalOptions = { ...defaultOptions, ...options };

    try {
      AdminUtils.showLoading();
      const response = await fetch(url, finalOptions);
      const data = await response.json();

      if (!response.ok) {
        throw new Error(data.message || 'حدث خطأ في الخادم');
      }

      return data;
    } catch (error) {
      console.error('Admin API Error:', error);
      AdminUtils.showToast(error.message || 'حدث خطأ غير متوقع', 'danger');
      throw error;
    } finally {
      AdminUtils.hideLoading();
    }
  },

  // Dashboard endpoints
  dashboard: {
    async getStats() {
      return AdminAPI.call('/admin/dashboard');
    },

    async getRecentOrders(limit = 10) {
      return AdminAPI.call(`/admin/orders?limit=${limit}&sort=-createdAt`);
    }
  },

  // Products endpoints
  products: {
    async getAll(params = {}) {
      const queryString = new URLSearchParams(params).toString();
      return AdminAPI.call(`/products?${queryString}`);
    },

    async getById(id) {
      return AdminAPI.call(`/products/${id}`);
    },

    async create(productData) {
      return AdminAPI.call('/products', {
        method: 'POST',
        body: JSON.stringify(productData)
      });
    },

    async update(id, productData) {
      return AdminAPI.call(`/products/${id}`, {
        method: 'PUT',
        body: JSON.stringify(productData)
      });
    },

    async delete(id) {
      return AdminAPI.call(`/products/${id}`, {
        method: 'DELETE'
      });
    }
  },

  // Categories endpoints
  categories: {
    async getAll() {
      return AdminAPI.call('/categories');
    },

    async create(categoryData) {
      return AdminAPI.call('/categories', {
        method: 'POST',
        body: JSON.stringify(categoryData)
      });
    },

    async update(id, categoryData) {
      return AdminAPI.call(`/categories/${id}`, {
        method: 'PUT',
        body: JSON.stringify(categoryData)
      });
    },

    async delete(id) {
      return AdminAPI.call(`/categories/${id}`, {
        method: 'DELETE'
      });
    }
  },

  // Orders endpoints
  orders: {
    async getAll(params = {}) {
      const queryString = new URLSearchParams(params).toString();
      return AdminAPI.call(`/admin/orders?${queryString}`);
    },

    async getById(id) {
      return AdminAPI.call(`/admin/orders/${id}`);
    },

    async updateStatus(id, status) {
      return AdminAPI.call(`/admin/orders/${id}/status`, {
        method: 'PUT',
        body: JSON.stringify({ status })
      });
    }
  },

  // Users endpoints
  users: {
    async getAll(params = {}) {
      const queryString = new URLSearchParams(params).toString();
      return AdminAPI.call(`/admin/users?${queryString}`);
    },

    async getById(id) {
      return AdminAPI.call(`/admin/users/${id}`);
    },

    async updateRole(id, role) {
      return AdminAPI.call(`/admin/users/${id}/role`, {
        method: 'PUT',
        body: JSON.stringify({ role })
      });
    },

    async toggleStatus(id) {
      return AdminAPI.call(`/admin/users/${id}/toggle-status`, {
        method: 'PUT'
      });
    }
  }
};

// Admin Utilities
const AdminUtils = {
  // Show loading spinner
  showLoading() {
    AdminState.isLoading = true;
    const spinner = document.getElementById('loadingSpinner');
    if (spinner) {
      spinner.classList.remove('d-none');
    }
  },

  // Hide loading spinner
  hideLoading() {
    AdminState.isLoading = false;
    const spinner = document.getElementById('loadingSpinner');
    if (spinner) {
      spinner.classList.add('d-none');
    }
  },

  // Show toast notification
  showToast(message, type = 'success') {
    // Create toast element
    const toastHtml = `
      <div class="toast align-items-center text-white bg-${type} border-0" role="alert">
        <div class="d-flex">
          <div class="toast-body">
            ${message}
          </div>
          <button type="button" class="btn-close btn-close-white me-2 m-auto" data-bs-dismiss="toast"></button>
        </div>
      </div>
    `;

    // Add to toast container or create one
    let toastContainer = document.getElementById('adminToastContainer');
    if (!toastContainer) {
      toastContainer = document.createElement('div');
      toastContainer.id = 'adminToastContainer';
      toastContainer.className = 'toast-container position-fixed top-0 end-0 p-3';
      toastContainer.style.zIndex = '9999';
      document.body.appendChild(toastContainer);
    }

    toastContainer.insertAdjacentHTML('beforeend', toastHtml);

    // Initialize and show toast
    const toastElement = toastContainer.lastElementChild;
    const toast = new bootstrap.Toast(toastElement, { delay: 4000 });
    toast.show();

    // Remove toast element after it's hidden
    toastElement.addEventListener('hidden.bs.toast', () => {
      toastElement.remove();
    });
  },

  // Format currency
  formatCurrency(amount) {
    return new Intl.NumberFormat('ar-SA', {
      style: 'currency',
      currency: 'SAR',
      minimumFractionDigits: 0
    }).format(amount);
  },

  // Format date
  formatDate(date, options = {}) {
    const defaultOptions = {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    };
    
    return new Intl.DateTimeFormat('ar-SA', { ...defaultOptions, ...options })
      .format(new Date(date));
  },

  // Format number
  formatNumber(number) {
    return new Intl.NumberFormat('ar-SA').format(number);
  },

  // Get status badge HTML
  getStatusBadge(status, type = 'order') {
    const statusConfig = {
      order: {
        pending: { class: 'bg-warning', text: 'في الانتظار' },
        confirmed: { class: 'bg-info', text: 'مؤكد' },
        processing: { class: 'bg-primary', text: 'قيد التجهيز' },
        shipped: { class: 'bg-secondary', text: 'تم الشحن' },
        delivered: { class: 'bg-success', text: 'تم التسليم' },
        cancelled: { class: 'bg-danger', text: 'ملغي' },
        refunded: { class: 'bg-dark', text: 'مسترد' }
      },
      user: {
        active: { class: 'bg-success', text: 'نشط' },
        inactive: { class: 'bg-secondary', text: 'غير نشط' },
        banned: { class: 'bg-danger', text: 'محظور' }
      },
      product: {
        active: { class: 'bg-success', text: 'نشط' },
        inactive: { class: 'bg-secondary', text: 'غير نشط' },
        draft: { class: 'bg-warning', text: 'مسودة' },
        archived: { class: 'bg-dark', text: 'مؤرشف' }
      }
    };

    const config = statusConfig[type]?.[status] || { class: 'bg-secondary', text: status };
    return `<span class="badge ${config.class}">${config.text}</span>`;
  },

  // Confirm dialog
  async confirm(message, title = 'تأكيد') {
    return new Promise((resolve) => {
      const modalHtml = `
        <div class="modal fade" id="confirmModal" tabindex="-1">
          <div class="modal-dialog">
            <div class="modal-content">
              <div class="modal-header">
                <h5 class="modal-title">${title}</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
              </div>
              <div class="modal-body">
                <p>${message}</p>
              </div>
              <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                <button type="button" class="btn btn-primary" id="confirmBtn">تأكيد</button>
              </div>
            </div>
          </div>
        </div>
      `;

      document.body.insertAdjacentHTML('beforeend', modalHtml);
      const modal = new bootstrap.Modal(document.getElementById('confirmModal'));
      
      document.getElementById('confirmBtn').addEventListener('click', () => {
        modal.hide();
        resolve(true);
      });

      modal._element.addEventListener('hidden.bs.modal', () => {
        document.getElementById('confirmModal').remove();
        resolve(false);
      });

      modal.show();
    });
  },

  // Debounce function
  debounce(func, wait) {
    let timeout;
    return function executedFunction(...args) {
      const later = () => {
        clearTimeout(timeout);
        func(...args);
      };
      clearTimeout(timeout);
      timeout = setTimeout(later, wait);
    };
  }
};

// Admin Authentication
const AdminAuth = {
  // Check if user is admin
  isAdmin() {
    const user = AdminState.user || JSON.parse(localStorage.getItem('user') || 'null');
    return user && ['admin', 'manager'].includes(user.role);
  },

  // Get current admin user
  async getCurrentUser() {
    if (!Auth.isLoggedIn()) {
      this.redirectToLogin();
      return null;
    }

    try {
      const response = await API.auth.getMe();
      AdminState.user = response.data;
      
      if (!this.isAdmin()) {
        AdminUtils.showToast('ليس لديك صلاحية للوصول إلى لوحة التحكم', 'danger');
        setTimeout(() => {
          window.location.href = '/';
        }, 2000);
        return null;
      }

      this.updateUI();
      return AdminState.user;
    } catch (error) {
      this.redirectToLogin();
      return null;
    }
  },

  // Redirect to login
  redirectToLogin() {
    const currentPath = window.location.pathname;
    window.location.href = `/login?redirect=${encodeURIComponent(currentPath)}`;
  },

  // Update UI based on user
  updateUI() {
    const adminUserName = document.getElementById('adminUserName');
    if (adminUserName && AdminState.user) {
      adminUserName.textContent = AdminState.user.name;
    }
  }
};

// Initialize admin panel
document.addEventListener('DOMContentLoaded', async () => {
  console.log('🔧 Admin panel initialized');

  // Check authentication
  const user = await AdminAuth.getCurrentUser();
  if (!user) return;

  // Update current date/time
  const updateDateTime = () => {
    const now = new Date();
    const dateTimeElement = document.getElementById('currentDateTime');
    if (dateTimeElement) {
      dateTimeElement.textContent = AdminUtils.formatDate(now, {
        weekday: 'long',
        year: 'numeric',
        month: 'long',
        day: 'numeric',
        hour: '2-digit',
        minute: '2-digit'
      });
    }
  };

  updateDateTime();
  setInterval(updateDateTime, 60000); // Update every minute

  // Add animation classes
  document.querySelectorAll('.card').forEach((card, index) => {
    card.classList.add('fade-in-up');
    card.style.animationDelay = `${index * 0.1}s`;
  });

  // Intersection Observer for animations
  const observer = new IntersectionObserver((entries) => {
    entries.forEach(entry => {
      if (entry.isIntersecting) {
        entry.target.classList.add('visible');
      }
    });
  }, { threshold: 0.1 });

  document.querySelectorAll('.fade-in-up, .slide-in-right').forEach(el => {
    observer.observe(el);
  });
});

// Global admin logout function
window.logout = async () => {
  const confirmed = await AdminUtils.confirm('هل أنت متأكد من تسجيل الخروج؟');
  if (confirmed) {
    await Auth.logout();
  }
};

// Export for use in other admin files
window.AdminAPI = AdminAPI;
window.AdminUtils = AdminUtils;
window.AdminAuth = AdminAuth;
window.AdminState = AdminState;
window.AdminConfig = AdminConfig;
