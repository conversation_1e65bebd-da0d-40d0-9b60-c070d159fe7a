{"name": "mongodb-memory-server", "version": "8.16.1", "description": "MongoDB Server for testing (auto-download latest version). The server will allow you to connect your favourite ODM or client library to the MongoDB Server and run parallel integration tests isolated from each other.", "main": "index.js", "types": "index.d.ts", "license": "MIT", "repository": {"type": "git", "url": "https://github.com/typegoose/mongodb-memory-server.git", "directory": "packages/mongodb-memory-server"}, "engines": {"node": ">=12.22.0"}, "homepage": "https://github.com/typegoose/mongodb-memory-server", "keywords": ["mongodb", "mongoose", "mock", "stub", "mockgoose", "mongodb-prebuilt", "mongomem"], "dependencies": {"mongodb-memory-server-core": "8.16.1", "tslib": "^2.6.1"}, "scripts": {"postinstall": "node ./postinstall.js"}}