const express = require('express');
const {
  getProducts,
  getProduct,
  createProduct,
  updateProduct,
  deleteProduct,
  getProductsByCategory,
  searchProducts,
  getFeaturedProducts,
  getRelatedProducts
} = require('../controllers/productController');

const { protect, authorize, optionalAuth } = require('../middleware/auth');
const { uploadProductImages } = require('../middleware/upload');
const {
  validateProduct,
  validateProductUpdate,
  validateObjectId,
  validatePagination,
  validateSearch
} = require('../middleware/validation');

const router = express.Router();

/**
 * @swagger
 * components:
 *   schemas:
 *     Product:
 *       type: object
 *       required:
 *         - name
 *         - description
 *         - price
 *         - category
 *       properties:
 *         name:
 *           type: object
 *           properties:
 *             ar:
 *               type: string
 *               description: اسم المنتج بالعربية
 *             en:
 *               type: string
 *               description: اسم المنتج بالإنجليزية
 *         description:
 *           type: object
 *           properties:
 *             ar:
 *               type: string
 *               description: وصف المنتج بالعربية
 *             en:
 *               type: string
 *               description: وصف المنتج بالإنجليزية
 *         price:
 *           type: number
 *           minimum: 0
 *           description: سعر المنتج
 *         comparePrice:
 *           type: number
 *           minimum: 0
 *           description: سعر المقارنة
 *         category:
 *           type: string
 *           description: معرف الفئة
 *         brand:
 *           type: string
 *           description: العلامة التجارية
 *         sku:
 *           type: string
 *           description: رمز المنتج
 *         stock:
 *           type: object
 *           properties:
 *             quantity:
 *               type: number
 *               minimum: 0
 *               description: الكمية المتاحة
 *         status:
 *           type: string
 *           enum: [active, inactive, draft, archived]
 *           description: حالة المنتج
 *         featured:
 *           type: boolean
 *           description: منتج مميز
 */

/**
 * @swagger
 * /api/products:
 *   get:
 *     summary: الحصول على قائمة المنتجات
 *     tags: [Products]
 *     parameters:
 *       - in: query
 *         name: page
 *         schema:
 *           type: integer
 *           minimum: 1
 *         description: رقم الصفحة
 *       - in: query
 *         name: limit
 *         schema:
 *           type: integer
 *           minimum: 1
 *           maximum: 100
 *         description: عدد النتائج في الصفحة
 *       - in: query
 *         name: sort
 *         schema:
 *           type: string
 *         description: ترتيب النتائج
 *       - in: query
 *         name: category
 *         schema:
 *           type: string
 *         description: فلترة حسب الفئة
 *       - in: query
 *         name: minPrice
 *         schema:
 *           type: number
 *         description: الحد الأدنى للسعر
 *       - in: query
 *         name: maxPrice
 *         schema:
 *           type: number
 *         description: الحد الأقصى للسعر
 *       - in: query
 *         name: brand
 *         schema:
 *           type: string
 *         description: فلترة حسب العلامة التجارية
 *       - in: query
 *         name: featured
 *         schema:
 *           type: boolean
 *         description: المنتجات المميزة فقط
 *     responses:
 *       200:
 *         description: قائمة المنتجات
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                 count:
 *                   type: integer
 *                 total:
 *                   type: integer
 *                 pagination:
 *                   type: object
 *                 data:
 *                   type: array
 *                   items:
 *                     $ref: '#/components/schemas/Product'
 */
router.get('/', validatePagination, optionalAuth, getProducts);

/**
 * @swagger
 * /api/products/featured:
 *   get:
 *     summary: الحصول على المنتجات المميزة
 *     tags: [Products]
 *     parameters:
 *       - in: query
 *         name: limit
 *         schema:
 *           type: integer
 *           minimum: 1
 *           maximum: 50
 *         description: عدد المنتجات المطلوبة
 *     responses:
 *       200:
 *         description: المنتجات المميزة
 */
router.get('/featured', getFeaturedProducts);

/**
 * @swagger
 * /api/products/search:
 *   get:
 *     summary: البحث في المنتجات
 *     tags: [Products]
 *     parameters:
 *       - in: query
 *         name: q
 *         required: true
 *         schema:
 *           type: string
 *         description: كلمة البحث
 *       - in: query
 *         name: category
 *         schema:
 *           type: string
 *         description: فلترة حسب الفئة
 *       - in: query
 *         name: minPrice
 *         schema:
 *           type: number
 *         description: الحد الأدنى للسعر
 *       - in: query
 *         name: maxPrice
 *         schema:
 *           type: number
 *         description: الحد الأقصى للسعر
 *     responses:
 *       200:
 *         description: نتائج البحث
 *       400:
 *         description: كلمة البحث مطلوبة
 */
router.get('/search', validateSearch, searchProducts);

/**
 * @swagger
 * /api/products/category/{categoryId}:
 *   get:
 *     summary: الحصول على منتجات فئة معينة
 *     tags: [Products]
 *     parameters:
 *       - in: path
 *         name: categoryId
 *         required: true
 *         schema:
 *           type: string
 *         description: معرف الفئة
 *     responses:
 *       200:
 *         description: منتجات الفئة
 *       404:
 *         description: الفئة غير موجودة
 */
router.get('/category/:categoryId', validateObjectId('categoryId'), getProductsByCategory);

/**
 * @swagger
 * /api/products/{id}:
 *   get:
 *     summary: الحصول على منتج واحد
 *     tags: [Products]
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *         description: معرف المنتج
 *     responses:
 *       200:
 *         description: تفاصيل المنتج
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                 data:
 *                   $ref: '#/components/schemas/Product'
 *       404:
 *         description: المنتج غير موجود
 */
router.get('/:id', validateObjectId('id'), optionalAuth, getProduct);

/**
 * @swagger
 * /api/products/{id}/related:
 *   get:
 *     summary: الحصول على المنتجات ذات الصلة
 *     tags: [Products]
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *         description: معرف المنتج
 *       - in: query
 *         name: limit
 *         schema:
 *           type: integer
 *           minimum: 1
 *           maximum: 20
 *         description: عدد المنتجات المطلوبة
 *     responses:
 *       200:
 *         description: المنتجات ذات الصلة
 *       404:
 *         description: المنتج غير موجود
 */
router.get('/:id/related', validateObjectId('id'), getRelatedProducts);

/**
 * @swagger
 * /api/products:
 *   post:
 *     summary: إنشاء منتج جديد
 *     tags: [Products]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         multipart/form-data:
 *           schema:
 *             type: object
 *             required:
 *               - name[ar]
 *               - description[ar]
 *               - price
 *               - category
 *             properties:
 *               name[ar]:
 *                 type: string
 *                 description: اسم المنتج بالعربية
 *               name[en]:
 *                 type: string
 *                 description: اسم المنتج بالإنجليزية
 *               description[ar]:
 *                 type: string
 *                 description: وصف المنتج بالعربية
 *               description[en]:
 *                 type: string
 *                 description: وصف المنتج بالإنجليزية
 *               price:
 *                 type: number
 *                 description: سعر المنتج
 *               category:
 *                 type: string
 *                 description: معرف الفئة
 *               images:
 *                 type: array
 *                 items:
 *                   type: string
 *                   format: binary
 *                 description: صور المنتج
 *     responses:
 *       201:
 *         description: تم إنشاء المنتج بنجاح
 *       400:
 *         description: خطأ في البيانات
 *       401:
 *         description: غير مصرح
 *       403:
 *         description: ليس لديك صلاحية
 */
router.post('/', protect, authorize('admin', 'manager'), uploadProductImages, validateProduct, createProduct);

/**
 * @swagger
 * /api/products/{id}:
 *   put:
 *     summary: تحديث منتج
 *     tags: [Products]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *         description: معرف المنتج
 *     requestBody:
 *       required: true
 *       content:
 *         multipart/form-data:
 *           schema:
 *             type: object
 *             properties:
 *               name[ar]:
 *                 type: string
 *               name[en]:
 *                 type: string
 *               description[ar]:
 *                 type: string
 *               description[en]:
 *                 type: string
 *               price:
 *                 type: number
 *               category:
 *                 type: string
 *               images:
 *                 type: array
 *                 items:
 *                   type: string
 *                   format: binary
 *               replaceImages:
 *                 type: boolean
 *                 description: استبدال الصور الموجودة
 *     responses:
 *       200:
 *         description: تم تحديث المنتج بنجاح
 *       404:
 *         description: المنتج غير موجود
 *       401:
 *         description: غير مصرح
 */
router.put('/:id', protect, authorize('admin', 'manager'), validateObjectId('id'), uploadProductImages, validateProductUpdate, updateProduct);

/**
 * @swagger
 * /api/products/{id}:
 *   delete:
 *     summary: حذف منتج
 *     tags: [Products]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *         description: معرف المنتج
 *     responses:
 *       200:
 *         description: تم حذف المنتج بنجاح
 *       404:
 *         description: المنتج غير موجود
 *       401:
 *         description: غير مصرح
 */
router.delete('/:id', protect, authorize('admin'), validateObjectId('id'), deleteProduct);

module.exports = router;
