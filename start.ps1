# Arabic E-commerce Platform - PowerShell Start Script
# Set console encoding to UTF-8 for Arabic text
[Console]::OutputEncoding = [System.Text.Encoding]::UTF8

# Colors
$Green = "Green"
$Red = "Red"
$Yellow = "Yellow"
$Cyan = "Cyan"
$White = "White"

function Write-ColorText {
    param(
        [string]$Text,
        [string]$Color = "White"
    )
    Write-Host $Text -ForegroundColor $Color
}

function Show-Banner {
    Write-Host ""
    Write-ColorText "╔══════════════════════════════════════════════════════════════╗" $Cyan
    Write-ColorText "║                                                              ║" $Cyan
    Write-ColorText "║           🛍️  Arabic E-commerce Platform 🛍️                  ║" $Cyan
    Write-ColorText "║                                                              ║" $Cyan
    Write-ColorText "║                    PowerShell Start Script                   ║" $Cyan
    Write-ColorText "║                                                              ║" $Cyan
    Write-ColorText "╚══════════════════════════════════════════════════════════════╝" $Cyan
    Write-Host ""
}

function Test-Command {
    param([string]$Command)
    try {
        Get-Command $Command -ErrorAction Stop | Out-Null
        return $true
    }
    catch {
        return $false
    }
}

function Test-Prerequisites {
    Write-ColorText "🔍 Checking prerequisites..." $Cyan
    
    # Check Node.js
    if (Test-Command "node") {
        Write-ColorText "✅ Node.js is installed" $Green
        $nodeVersion = node --version
        Write-ColorText "   Version: $nodeVersion" $White
    }
    else {
        Write-ColorText "❌ Node.js is not installed!" $Red
        Write-ColorText "" $White
        Write-ColorText "📥 Please install Node.js first:" $Yellow
        Write-ColorText "   1. Go to https://nodejs.org/" $White
        Write-ColorText "   2. Download and install the LTS version" $White
        Write-ColorText "   3. Restart PowerShell and run this script again" $White
        Write-ColorText "" $White
        Read-Host "Press Enter to exit"
        exit 1
    }
    
    # Check npm
    if (Test-Command "npm") {
        Write-ColorText "✅ npm is available" $Green
        $npmVersion = npm --version
        Write-ColorText "   Version: $npmVersion" $White
    }
    else {
        Write-ColorText "❌ npm is not available!" $Red
        Write-ColorText "Please reinstall Node.js" $Yellow
        Read-Host "Press Enter to exit"
        exit 1
    }
    
    # Check MongoDB (optional)
    Write-Host ""
    Write-ColorText "🗄️  Checking MongoDB..." $Cyan
    if (Test-Command "mongod") {
        Write-ColorText "✅ MongoDB is available" $Green
    }
    else {
        Write-ColorText "⚠️  MongoDB is not installed or not in PATH" $Yellow
        Write-ColorText "   You can either:" $White
        Write-ColorText "   1. Install MongoDB Community Server locally" $White
        Write-ColorText "   2. Use MongoDB Atlas (cloud database)" $White
        Write-ColorText "   3. Update MONGODB_URI in .env file" $White
    }
}

function Install-Dependencies {
    Write-Host ""
    Write-ColorText "📦 Installing dependencies..." $Cyan
    
    try {
        npm install
        if ($LASTEXITCODE -eq 0) {
            Write-ColorText "✅ Dependencies installed successfully!" $Green
        }
        else {
            throw "npm install failed"
        }
    }
    catch {
        Write-ColorText "❌ Failed to install dependencies" $Red
        Read-Host "Press Enter to exit"
        exit 1
    }
}

function Setup-Environment {
    Write-Host ""
    if (-not (Test-Path ".env")) {
        Write-ColorText "⚙️  Creating .env file..." $Cyan
        if (Test-Path ".env.example") {
            Copy-Item ".env.example" ".env"
            Write-ColorText "✅ .env file created from .env.example" $Green
        }
        else {
            Write-ColorText "❌ .env.example not found" $Red
            Write-ColorText "Please create .env file manually" $Yellow
            Read-Host "Press Enter to exit"
            exit 1
        }
    }
    else {
        Write-ColorText "✅ .env file exists" $Green
    }
}

function Seed-Database {
    Write-Host ""
    $seedChoice = Read-Host "🌱 Do you want to seed the database with sample data? (y/n)"
    
    if ($seedChoice -eq "y" -or $seedChoice -eq "Y") {
        Write-Host ""
        Write-ColorText "🌱 Seeding database..." $Cyan
        
        try {
            npm run seed
            if ($LASTEXITCODE -eq 0) {
                Write-ColorText "✅ Database seeded successfully!" $Green
            }
            else {
                Write-ColorText "⚠️  Seeding failed - you can run 'npm run seed' later" $Yellow
            }
        }
        catch {
            Write-ColorText "⚠️  Seeding failed - you can run 'npm run seed' later" $Yellow
        }
    }
}

function Start-Server {
    Write-Host ""
    Write-ColorText "🚀 Starting the server..." $Cyan
    Write-Host ""
    Write-ColorText "🌐 The application will be available at:" $Cyan
    Write-ColorText "   Main Site: http://localhost:3000" $White
    Write-ColorText "   Admin Panel: http://localhost:3000/admin" $White
    Write-ColorText "   API Docs: http://localhost:3000/api-docs" $White
    Write-Host ""
    Write-ColorText "👤 Test Accounts:" $Cyan
    Write-ColorText "   Admin: <EMAIL> / admin123456" $White
    Write-ColorText "   User: <EMAIL> / user123456" $White
    Write-Host ""
    Write-ColorText "Press Ctrl+C to stop the server" $Yellow
    Write-Host ""
    
    # Start the server
    npm start
}

# Main execution
try {
    Show-Banner
    Test-Prerequisites
    Install-Dependencies
    Setup-Environment
    Seed-Database
    Start-Server
}
catch {
    Write-ColorText "❌ An error occurred: $($_.Exception.Message)" $Red
    Read-Host "Press Enter to exit"
    exit 1
}

Read-Host "Press Enter to exit"
