#!/usr/bin/env node

/**
 * Quick Start Script for Arabic E-commerce Platform
 * This script helps users quickly set up and run the platform
 */

const { spawn } = require('child_process');
const path = require('path');
const fs = require('fs');
const readline = require('readline');

// Colors for console output
const colors = {
  reset: '\x1b[0m',
  bright: '\x1b[1m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  magenta: '\x1b[35m',
  cyan: '\x1b[36m'
};

const log = (message, color = 'reset') => {
  console.log(`${colors[color]}${message}${colors.reset}`);
};

const rl = readline.createInterface({
  input: process.stdin,
  output: process.stdout
});

const question = (query) => {
  return new Promise(resolve => rl.question(query, resolve));
};

// Display welcome banner
function displayWelcome() {
  const banner = `
${colors.cyan}
╔══════════════════════════════════════════════════════════════╗
║                                                              ║
║           🛍️  Arabic E-commerce Platform 🛍️                  ║
║                                                              ║
║                    Quick Start Setup                         ║
║                                                              ║
╚══════════════════════════════════════════════════════════════╝
${colors.reset}`;

  console.log(banner);
  log('مرحباً بك في منصة التجارة الإلكترونية العربية!', 'bright');
  log('سنقوم بإعداد المشروع خطوة بخطوة...', 'cyan');
  console.log('');
}

// Check if file exists
function fileExists(filePath) {
  return fs.existsSync(path.join(__dirname, '..', filePath));
}

// Check prerequisites
async function checkPrerequisites() {
  log('🔍 فحص المتطلبات الأساسية...', 'cyan');

  // Check Node.js version
  const nodeVersion = process.version;
  const majorVersion = parseInt(nodeVersion.slice(1).split('.')[0]);
  
  if (majorVersion < 16) {
    log(`❌ يتطلب Node.js الإصدار 16 أو أحدث. الإصدار الحالي: ${nodeVersion}`, 'red');
    return false;
  }
  
  log(`✅ Node.js ${nodeVersion}`, 'green');

  // Check if MongoDB is running (optional check)
  try {
    const { spawn } = require('child_process');
    const mongoCheck = spawn('mongod', ['--version'], { stdio: 'pipe' });
    
    mongoCheck.on('close', (code) => {
      if (code === 0) {
        log('✅ MongoDB متاح', 'green');
      } else {
        log('⚠️  MongoDB غير متاح - تأكد من تثبيته وتشغيله', 'yellow');
      }
    });
  } catch (error) {
    log('⚠️  لم يتم العثور على MongoDB - تأكد من تثبيته', 'yellow');
  }

  return true;
}

// Setup environment file
async function setupEnvironment() {
  log('⚙️  إعداد ملف البيئة...', 'cyan');

  if (!fileExists('.env')) {
    if (fileExists('.env.example')) {
      // Copy .env.example to .env
      const envExample = fs.readFileSync(path.join(__dirname, '..', '.env.example'), 'utf8');
      fs.writeFileSync(path.join(__dirname, '..', '.env'), envExample);
      log('✅ تم إنشاء ملف .env من .env.example', 'green');
    } else {
      // Create basic .env file
      const basicEnv = `
NODE_ENV=development
PORT=3000
MONGODB_URI=mongodb://localhost:27017/arabic_ecommerce
JWT_SECRET=your_super_secret_jwt_key_here_change_in_production
EMAIL_HOST=smtp.gmail.com
EMAIL_PORT=587
EMAIL_USER=<EMAIL>
EMAIL_PASS=your_app_password
EMAIL_FROM=<EMAIL>
ADMIN_EMAIL=<EMAIL>
ADMIN_PASSWORD=admin123456
STORE_NAME=متجرنا الإلكتروني
STORE_CURRENCY=SAR
STORE_LANGUAGE=ar
`.trim();

      fs.writeFileSync(path.join(__dirname, '..', '.env'), basicEnv);
      log('✅ تم إنشاء ملف .env أساسي', 'green');
    }
  } else {
    log('✅ ملف .env موجود بالفعل', 'green');
  }

  log('💡 يرجى تحديث ملف .env بالإعدادات الصحيحة قبل التشغيل', 'yellow');
}

// Install dependencies
async function installDependencies() {
  log('📦 تثبيت التبعيات...', 'cyan');

  if (!fileExists('node_modules')) {
    const installChoice = await question('هل تريد تثبيت التبعيات الآن؟ (y/n): ');
    
    if (installChoice.toLowerCase() === 'y' || installChoice.toLowerCase() === 'yes') {
      return new Promise((resolve, reject) => {
        const npm = spawn('npm', ['install'], {
          cwd: path.join(__dirname, '..'),
          stdio: 'inherit'
        });

        npm.on('close', (code) => {
          if (code === 0) {
            log('✅ تم تثبيت التبعيات بنجاح!', 'green');
            resolve(true);
          } else {
            log('❌ فشل في تثبيت التبعيات', 'red');
            reject(false);
          }
        });
      });
    } else {
      log('⏭️  تم تخطي تثبيت التبعيات', 'yellow');
      return false;
    }
  } else {
    log('✅ التبعيات مثبتة بالفعل', 'green');
    return true;
  }
}

// Setup database
async function setupDatabase() {
  log('🗄️  إعداد قاعدة البيانات...', 'cyan');

  const seedChoice = await question('هل تريد إضافة بيانات تجريبية؟ (y/n): ');
  
  if (seedChoice.toLowerCase() === 'y' || seedChoice.toLowerCase() === 'yes') {
    return new Promise((resolve, reject) => {
      const seed = spawn('npm', ['run', 'seed'], {
        cwd: path.join(__dirname, '..'),
        stdio: 'inherit'
      });

      seed.on('close', (code) => {
        if (code === 0) {
          log('✅ تم إضافة البيانات التجريبية بنجاح!', 'green');
          resolve(true);
        } else {
          log('❌ فشل في إضافة البيانات التجريبية', 'red');
          reject(false);
        }
      });
    });
  } else {
    log('⏭️  تم تخطي إضافة البيانات التجريبية', 'yellow');
    return false;
  }
}

// Display final instructions
function displayInstructions() {
  log('\n🎉 تم إعداد المشروع بنجاح!', 'bright');
  console.log('');
  
  log('📋 الخطوات التالية:', 'cyan');
  log('1. تأكد من تشغيل MongoDB على جهازك', 'white');
  log('2. قم بتحديث ملف .env بالإعدادات الصحيحة', 'white');
  log('3. شغل المشروع باستخدام أحد الأوامر التالية:', 'white');
  console.log('');
  
  log('🚀 أوامر التشغيل:', 'magenta');
  log('   npm start          - تشغيل الخادم', 'white');
  log('   npm run dev        - تشغيل في وضع التطوير', 'white');
  log('   npm run setup      - تشغيل الإعداد السريع', 'white');
  console.log('');
  
  log('🌐 الروابط المهمة:', 'magenta');
  log('   الموقع الرئيسي:     http://localhost:3000', 'white');
  log('   لوحة التحكم:       http://localhost:3000/admin', 'white');
  log('   توثيق API:         http://localhost:3000/api-docs', 'white');
  console.log('');
  
  log('👤 حسابات تجريبية:', 'magenta');
  log('   المدير:           <EMAIL> / admin123456', 'white');
  log('   مستخدم:          <EMAIL> / user123456', 'white');
  console.log('');
  
  log('💡 نصائح:', 'yellow');
  log('• راجع ملف README.md للمزيد من المعلومات', 'white');
  log('• استخدم npm run seed لإضافة بيانات تجريبية', 'white');
  log('• تأكد من إعداد البريد الإلكتروني في ملف .env', 'white');
  console.log('');
}

// Main setup function
async function main() {
  try {
    displayWelcome();

    // Check prerequisites
    const prereqsOk = await checkPrerequisites();
    if (!prereqsOk) {
      log('❌ يرجى تثبيت المتطلبات الأساسية أولاً', 'red');
      process.exit(1);
    }

    // Setup environment
    await setupEnvironment();

    // Install dependencies
    const depsInstalled = await installDependencies();

    // Setup database (only if dependencies are installed)
    if (depsInstalled) {
      await setupDatabase();
    }

    // Display final instructions
    displayInstructions();

    // Ask if user wants to start the server
    const startChoice = await question('هل تريد تشغيل الخادم الآن؟ (y/n): ');
    
    if (startChoice.toLowerCase() === 'y' || startChoice.toLowerCase() === 'yes') {
      log('🚀 بدء تشغيل الخادم...', 'cyan');
      
      const server = spawn('npm', ['start'], {
        cwd: path.join(__dirname, '..'),
        stdio: 'inherit'
      });

      // Handle Ctrl+C
      process.on('SIGINT', () => {
        log('\n🛑 إيقاف الخادم...', 'yellow');
        server.kill('SIGINT');
        process.exit(0);
      });
    }

  } catch (error) {
    log(`❌ حدث خطأ: ${error.message}`, 'red');
    process.exit(1);
  } finally {
    rl.close();
  }
}

// Run if this file is executed directly
if (require.main === module) {
  main();
}

module.exports = { main };
