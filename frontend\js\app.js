// Arabic E-commerce Platform - Main JavaScript File

// Global Configuration
const CONFIG = {
  API_BASE_URL: '/api',
  ITEMS_PER_PAGE: 12,
  CURRENCY: 'ريال',
  LANGUAGE: 'ar'
};

// Global State
const AppState = {
  user: null,
  cart: JSON.parse(localStorage.getItem('cart')) || [],
  categories: [],
  isLoading: false
};

// Utility Functions
const Utils = {
  // Show loading spinner
  showLoading() {
    AppState.isLoading = true;
    const spinner = document.getElementById('loadingSpinner');
    if (spinner) {
      spinner.classList.remove('d-none');
    }
  },

  // Hide loading spinner
  hideLoading() {
    AppState.isLoading = false;
    const spinner = document.getElementById('loadingSpinner');
    if (spinner) {
      spinner.classList.add('d-none');
    }
  },

  // Format price
  formatPrice(price) {
    return new Intl.NumberFormat('ar-SA', {
      style: 'currency',
      currency: 'SAR',
      minimumFractionDigits: 0
    }).format(price);
  },

  // Format date
  formatDate(date) {
    return new Intl.DateTimeFormat('ar-SA', {
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    }).format(new Date(date));
  },

  // Show toast notification
  showToast(message, type = 'success') {
    // Create toast element
    const toastHtml = `
      <div class="toast align-items-center text-white bg-${type} border-0" role="alert">
        <div class="d-flex">
          <div class="toast-body">
            ${message}
          </div>
          <button type="button" class="btn-close btn-close-white me-2 m-auto" data-bs-dismiss="toast"></button>
        </div>
      </div>
    `;

    // Add to toast container or create one
    let toastContainer = document.getElementById('toastContainer');
    if (!toastContainer) {
      toastContainer = document.createElement('div');
      toastContainer.id = 'toastContainer';
      toastContainer.className = 'toast-container position-fixed top-0 end-0 p-3';
      toastContainer.style.zIndex = '9999';
      document.body.appendChild(toastContainer);
    }

    toastContainer.insertAdjacentHTML('beforeend', toastHtml);

    // Initialize and show toast
    const toastElement = toastContainer.lastElementChild;
    const toast = new bootstrap.Toast(toastElement, { delay: 3000 });
    toast.show();

    // Remove toast element after it's hidden
    toastElement.addEventListener('hidden.bs.toast', () => {
      toastElement.remove();
    });
  },

  // Debounce function
  debounce(func, wait) {
    let timeout;
    return function executedFunction(...args) {
      const later = () => {
        clearTimeout(timeout);
        func(...args);
      };
      clearTimeout(timeout);
      timeout = setTimeout(later, wait);
    };
  },

  // Scroll to top
  scrollToTop() {
    window.scrollTo({
      top: 0,
      behavior: 'smooth'
    });
  }
};

// API Service
const API = {
  // Generic API call
  async call(endpoint, options = {}) {
    const url = `${CONFIG.API_BASE_URL}${endpoint}`;
    const defaultOptions = {
      headers: {
        'Content-Type': 'application/json'
      }
    };

    // Add auth token if available
    const token = localStorage.getItem('token');
    if (token) {
      defaultOptions.headers.Authorization = `Bearer ${token}`;
    }

    const finalOptions = { ...defaultOptions, ...options };

    try {
      Utils.showLoading();
      const response = await fetch(url, finalOptions);
      const data = await response.json();

      if (!response.ok) {
        throw new Error(data.message || 'حدث خطأ في الخادم');
      }

      return data;
    } catch (error) {
      console.error('API Error:', error);
      Utils.showToast(error.message || 'حدث خطأ غير متوقع', 'danger');
      throw error;
    } finally {
      Utils.hideLoading();
    }
  },

  // Auth endpoints
  auth: {
    async login(email, password) {
      return API.call('/auth/login', {
        method: 'POST',
        body: JSON.stringify({ email, password })
      });
    },

    async register(userData) {
      return API.call('/auth/register', {
        method: 'POST',
        body: JSON.stringify(userData)
      });
    },

    async getMe() {
      return API.call('/auth/me');
    },

    async logout() {
      return API.call('/auth/logout');
    }
  },

  // Products endpoints
  products: {
    async getAll(params = {}) {
      const queryString = new URLSearchParams(params).toString();
      return API.call(`/products?${queryString}`);
    },

    async getById(id) {
      return API.call(`/products/${id}`);
    },

    async getFeatured(limit = 8) {
      return API.call(`/products/featured?limit=${limit}`);
    },

    async search(query, params = {}) {
      const queryString = new URLSearchParams({ q: query, ...params }).toString();
      return API.call(`/products/search?${queryString}`);
    },

    async getByCategory(categoryId, params = {}) {
      const queryString = new URLSearchParams(params).toString();
      return API.call(`/products/category/${categoryId}?${queryString}`);
    }
  },

  // Categories endpoints
  categories: {
    async getAll() {
      return API.call('/categories');
    },

    async getFeatured(limit = 6) {
      return API.call(`/categories/featured?limit=${limit}`);
    },

    async getTree() {
      return API.call('/categories/tree');
    }
  }
};

// Cart Management
const Cart = {
  // Add item to cart
  addItem(product, quantity = 1, variant = null) {
    const existingItem = AppState.cart.find(item => 
      item.product._id === product._id && 
      JSON.stringify(item.variant) === JSON.stringify(variant)
    );

    if (existingItem) {
      existingItem.quantity += quantity;
    } else {
      AppState.cart.push({
        product,
        quantity,
        variant,
        addedAt: new Date().toISOString()
      });
    }

    this.saveToStorage();
    this.updateUI();
    Utils.showToast('تم إضافة المنتج إلى السلة', 'success');
  },

  // Remove item from cart
  removeItem(productId, variant = null) {
    AppState.cart = AppState.cart.filter(item => 
      !(item.product._id === productId && 
        JSON.stringify(item.variant) === JSON.stringify(variant))
    );

    this.saveToStorage();
    this.updateUI();
    Utils.showToast('تم حذف المنتج من السلة', 'info');
  },

  // Update item quantity
  updateQuantity(productId, quantity, variant = null) {
    const item = AppState.cart.find(item => 
      item.product._id === productId && 
      JSON.stringify(item.variant) === JSON.stringify(variant)
    );

    if (item) {
      if (quantity <= 0) {
        this.removeItem(productId, variant);
      } else {
        item.quantity = quantity;
        this.saveToStorage();
        this.updateUI();
      }
    }
  },

  // Clear cart
  clear() {
    AppState.cart = [];
    this.saveToStorage();
    this.updateUI();
    Utils.showToast('تم إفراغ السلة', 'info');
  },

  // Get cart total
  getTotal() {
    return AppState.cart.reduce((total, item) => {
      return total + (item.product.price * item.quantity);
    }, 0);
  },

  // Get cart count
  getCount() {
    return AppState.cart.reduce((count, item) => count + item.quantity, 0);
  },

  // Save to localStorage
  saveToStorage() {
    localStorage.setItem('cart', JSON.stringify(AppState.cart));
  },

  // Update UI elements
  updateUI() {
    const cartCount = document.getElementById('cartCount');
    if (cartCount) {
      const count = this.getCount();
      cartCount.textContent = count;
      cartCount.style.display = count > 0 ? 'block' : 'none';
    }
  }
};

// Authentication Management
const Auth = {
  // Check if user is logged in
  isLoggedIn() {
    return !!localStorage.getItem('token');
  },

  // Login user
  async login(email, password) {
    try {
      const response = await API.auth.login(email, password);
      
      localStorage.setItem('token', response.token);
      AppState.user = response.user;
      
      this.updateUI();
      Utils.showToast('تم تسجيل الدخول بنجاح', 'success');
      
      return response;
    } catch (error) {
      throw error;
    }
  },

  // Register user
  async register(userData) {
    try {
      const response = await API.auth.register(userData);
      
      localStorage.setItem('token', response.token);
      AppState.user = response.user;
      
      this.updateUI();
      Utils.showToast('تم إنشاء الحساب بنجاح', 'success');
      
      return response;
    } catch (error) {
      throw error;
    }
  },

  // Logout user
  async logout() {
    try {
      await API.auth.logout();
    } catch (error) {
      console.error('Logout error:', error);
    } finally {
      localStorage.removeItem('token');
      AppState.user = null;
      this.updateUI();
      Utils.showToast('تم تسجيل الخروج بنجاح', 'info');
      
      // Redirect to home page
      if (window.location.pathname !== '/') {
        window.location.href = '/';
      }
    }
  },

  // Get current user
  async getCurrentUser() {
    if (!this.isLoggedIn()) {
      return null;
    }

    try {
      const response = await API.auth.getMe();
      AppState.user = response.data;
      this.updateUI();
      return response.data;
    } catch (error) {
      // Token might be invalid
      this.logout();
      return null;
    }
  },

  // Update UI based on auth state
  updateUI() {
    const userLoggedInElements = document.querySelectorAll('.user-logged-in');
    const userNotLoggedInElements = document.querySelectorAll('.user-not-logged-in');

    if (this.isLoggedIn() && AppState.user) {
      userLoggedInElements.forEach(el => el.classList.remove('d-none'));
      userNotLoggedInElements.forEach(el => el.classList.add('d-none'));
    } else {
      userLoggedInElements.forEach(el => el.classList.add('d-none'));
      userNotLoggedInElements.forEach(el => el.classList.remove('d-none'));
    }
  }
};

// Search functionality
const Search = {
  init() {
    const searchForm = document.getElementById('searchForm');
    const searchInput = document.getElementById('searchInput');

    if (searchForm && searchInput) {
      searchForm.addEventListener('submit', (e) => {
        e.preventDefault();
        const query = searchInput.value.trim();
        if (query) {
          this.performSearch(query);
        }
      });

      // Add debounced search suggestions
      searchInput.addEventListener('input', Utils.debounce((e) => {
        const query = e.target.value.trim();
        if (query.length >= 2) {
          this.showSuggestions(query);
        } else {
          this.hideSuggestions();
        }
      }, 300));
    }
  },

  performSearch(query) {
    // Redirect to products page with search query
    window.location.href = `/products?search=${encodeURIComponent(query)}`;
  },

  async showSuggestions(query) {
    // Implementation for search suggestions
    // This would show a dropdown with suggested products
  },

  hideSuggestions() {
    // Hide search suggestions dropdown
  }
};

// Initialize app when DOM is loaded
document.addEventListener('DOMContentLoaded', async () => {
  console.log('🚀 Arabic E-commerce Platform Initialized');

  // Initialize components
  Search.init();
  Cart.updateUI();

  // Load current user if logged in
  if (Auth.isLoggedIn()) {
    await Auth.getCurrentUser();
  } else {
    Auth.updateUI();
  }

  // Load categories for navigation
  try {
    const categoriesResponse = await API.categories.getAll();
    AppState.categories = categoriesResponse.data;
    
    // Update categories dropdown in navigation
    const categoriesDropdown = document.getElementById('categoriesDropdown');
    if (categoriesDropdown && AppState.categories.length > 0) {
      categoriesDropdown.innerHTML = AppState.categories
        .slice(0, 8) // Show only first 8 categories
        .map(category => `
          <li>
            <a class="dropdown-item" href="/products?category=${category._id}">
              ${category.name.ar}
            </a>
          </li>
        `).join('');
      
      // Add "View All" link
      categoriesDropdown.innerHTML += `
        <li><hr class="dropdown-divider"></li>
        <li><a class="dropdown-item fw-bold" href="/products">عرض جميع الأقسام</a></li>
      `;
    }
  } catch (error) {
    console.error('Failed to load categories:', error);
  }

  // Add scroll animations
  const observerOptions = {
    threshold: 0.1,
    rootMargin: '0px 0px -50px 0px'
  };

  const observer = new IntersectionObserver((entries) => {
    entries.forEach(entry => {
      if (entry.isIntersecting) {
        entry.target.classList.add('visible');
      }
    });
  }, observerOptions);

  // Observe elements with animation classes
  document.querySelectorAll('.fade-in, .slide-in-left, .slide-in-right').forEach(el => {
    observer.observe(el);
  });
});

// Global logout function
window.logout = () => {
  Auth.logout();
};

// Export for use in other files
window.AppState = AppState;
window.Utils = Utils;
window.API = API;
window.Cart = Cart;
window.Auth = Auth;
