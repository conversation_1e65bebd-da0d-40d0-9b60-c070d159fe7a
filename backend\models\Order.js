const mongoose = require('mongoose');

const orderSchema = new mongoose.Schema({
  orderNumber: {
    type: String,
    unique: true,
    required: true
  },
  user: {
    type: mongoose.Schema.ObjectId,
    ref: 'User',
    required: [true, 'المستخدم مطلوب']
  },
  items: [{
    product: {
      type: mongoose.Schema.ObjectId,
      ref: 'Product',
      required: true
    },
    name: {
      ar: String,
      en: String
    },
    image: String,
    price: {
      type: Number,
      required: true,
      min: [0, 'السعر لا يمكن أن يكون سالباً']
    },
    quantity: {
      type: Number,
      required: true,
      min: [1, 'الكمية يجب أن تكون على الأقل 1']
    },
    variant: {
      name: String,
      value: String,
      sku: String
    },
    subtotal: {
      type: Number,
      required: true,
      min: [0, 'المجموع الفرعي لا يمكن أن يكون سالباً']
    }
  }],
  shippingAddress: {
    name: {
      type: String,
      required: [true, 'اسم المستلم مطلوب']
    },
    phone: {
      type: String,
      required: [true, 'رقم الهاتف مطلوب']
    },
    email: String,
    street: {
      type: String,
      required: [true, 'عنوان الشارع مطلوب']
    },
    city: {
      type: String,
      required: [true, 'المدينة مطلوبة']
    },
    state: {
      type: String,
      required: [true, 'المنطقة مطلوبة']
    },
    zipCode: {
      type: String,
      required: [true, 'الرمز البريدي مطلوب']
    },
    country: {
      type: String,
      default: 'Saudi Arabia'
    },
    notes: String
  },
  billingAddress: {
    name: String,
    phone: String,
    email: String,
    street: String,
    city: String,
    state: String,
    zipCode: String,
    country: String,
    sameAsShipping: {
      type: Boolean,
      default: true
    }
  },
  pricing: {
    subtotal: {
      type: Number,
      required: true,
      min: [0, 'المجموع الفرعي لا يمكن أن يكون سالباً']
    },
    tax: {
      type: Number,
      default: 0,
      min: [0, 'الضريبة لا يمكن أن تكون سالبة']
    },
    taxRate: {
      type: Number,
      default: 0.15, // 15% VAT in Saudi Arabia
      min: [0, 'معدل الضريبة لا يمكن أن يكون سالباً']
    },
    shipping: {
      type: Number,
      default: 0,
      min: [0, 'تكلفة الشحن لا يمكن أن تكون سالبة']
    },
    discount: {
      type: Number,
      default: 0,
      min: [0, 'الخصم لا يمكن أن يكون سالباً']
    },
    total: {
      type: Number,
      required: true,
      min: [0, 'المجموع الكلي لا يمكن أن يكون سالباً']
    }
  },
  coupon: {
    code: String,
    type: {
      type: String,
      enum: ['percentage', 'fixed']
    },
    value: Number,
    discount: Number
  },
  payment: {
    method: {
      type: String,
      enum: ['cash_on_delivery', 'credit_card', 'bank_transfer', 'wallet'],
      required: [true, 'طريقة الدفع مطلوبة']
    },
    status: {
      type: String,
      enum: ['pending', 'paid', 'failed', 'refunded', 'partially_refunded'],
      default: 'pending'
    },
    transactionId: String,
    paidAt: Date,
    refundedAt: Date,
    refundAmount: {
      type: Number,
      default: 0
    },
    gateway: String,
    gatewayResponse: mongoose.Schema.Types.Mixed
  },
  shipping: {
    method: {
      type: String,
      enum: ['standard', 'express', 'overnight', 'pickup'],
      default: 'standard'
    },
    cost: {
      type: Number,
      default: 0
    },
    estimatedDelivery: Date,
    trackingNumber: String,
    carrier: String,
    shippedAt: Date,
    deliveredAt: Date
  },
  status: {
    type: String,
    enum: ['pending', 'confirmed', 'processing', 'shipped', 'delivered', 'cancelled', 'refunded'],
    default: 'pending'
  },
  statusHistory: [{
    status: {
      type: String,
      required: true
    },
    note: String,
    updatedBy: {
      type: mongoose.Schema.ObjectId,
      ref: 'User'
    },
    updatedAt: {
      type: Date,
      default: Date.now
    }
  }],
  notes: {
    customer: String,
    admin: String,
    internal: String
  },
  source: {
    type: String,
    enum: ['website', 'mobile_app', 'admin', 'api'],
    default: 'website'
  },
  currency: {
    type: String,
    default: 'SAR'
  },
  language: {
    type: String,
    enum: ['ar', 'en'],
    default: 'ar'
  },
  customerInfo: {
    ip: String,
    userAgent: String,
    referrer: String
  },
  cancellation: {
    reason: String,
    cancelledBy: {
      type: mongoose.Schema.ObjectId,
      ref: 'User'
    },
    cancelledAt: Date,
    refundRequested: {
      type: Boolean,
      default: false
    }
  }
}, {
  timestamps: true,
  toJSON: { virtuals: true },
  toObject: { virtuals: true }
});

// Virtual for order age
orderSchema.virtual('age').get(function() {
  return Date.now() - this.createdAt;
});

// Virtual for total items count
orderSchema.virtual('totalItems').get(function() {
  return this.items.reduce((total, item) => total + item.quantity, 0);
});

// Virtual for order status in Arabic
orderSchema.virtual('statusAr').get(function() {
  const statusMap = {
    'pending': 'في الانتظار',
    'confirmed': 'مؤكد',
    'processing': 'قيد المعالجة',
    'shipped': 'تم الشحن',
    'delivered': 'تم التسليم',
    'cancelled': 'ملغي',
    'refunded': 'مسترد'
  };
  return statusMap[this.status] || this.status;
});

// Indexes for better performance
orderSchema.index({ orderNumber: 1 });
orderSchema.index({ user: 1 });
orderSchema.index({ status: 1 });
orderSchema.index({ 'payment.status': 1 });
orderSchema.index({ createdAt: -1 });
orderSchema.index({ 'pricing.total': 1 });

// Generate order number before saving
orderSchema.pre('save', async function(next) {
  if (!this.orderNumber) {
    const date = new Date();
    const year = date.getFullYear().toString().slice(-2);
    const month = (date.getMonth() + 1).toString().padStart(2, '0');
    const day = date.getDate().toString().padStart(2, '0');
    
    // Find the last order of today
    const startOfDay = new Date(date.getFullYear(), date.getMonth(), date.getDate());
    const endOfDay = new Date(date.getFullYear(), date.getMonth(), date.getDate() + 1);
    
    const lastOrder = await this.constructor.findOne({
      createdAt: { $gte: startOfDay, $lt: endOfDay }
    }).sort({ createdAt: -1 });
    
    let sequence = 1;
    if (lastOrder && lastOrder.orderNumber) {
      const lastSequence = parseInt(lastOrder.orderNumber.slice(-4));
      sequence = lastSequence + 1;
    }
    
    this.orderNumber = `ORD${year}${month}${day}${sequence.toString().padStart(4, '0')}`;
  }
  next();
});

// Calculate totals before saving
orderSchema.pre('save', function(next) {
  // Calculate subtotal
  this.pricing.subtotal = this.items.reduce((total, item) => total + item.subtotal, 0);
  
  // Calculate tax
  this.pricing.tax = this.pricing.subtotal * this.pricing.taxRate;
  
  // Calculate total
  this.pricing.total = this.pricing.subtotal + this.pricing.tax + this.pricing.shipping - this.pricing.discount;
  
  next();
});

// Add status to history when status changes
orderSchema.pre('save', function(next) {
  if (this.isModified('status') && !this.isNew) {
    this.statusHistory.push({
      status: this.status,
      updatedAt: new Date()
    });
  }
  next();
});

// Static method to get orders by status
orderSchema.statics.getByStatus = function(status, options = {}) {
  return this.find({ status })
    .populate('user', 'name email phone')
    .populate('items.product', 'name images')
    .sort(options.sort || { createdAt: -1 })
    .limit(options.limit || 50);
};

// Static method to get user orders
orderSchema.statics.getUserOrders = function(userId, options = {}) {
  return this.find({ user: userId })
    .populate('items.product', 'name images slug')
    .sort(options.sort || { createdAt: -1 })
    .limit(options.limit || 20);
};

// Static method to get sales statistics
orderSchema.statics.getSalesStats = async function(startDate, endDate) {
  const stats = await this.aggregate([
    {
      $match: {
        createdAt: { $gte: startDate, $lte: endDate },
        status: { $in: ['delivered', 'shipped'] },
        'payment.status': 'paid'
      }
    },
    {
      $group: {
        _id: null,
        totalOrders: { $sum: 1 },
        totalRevenue: { $sum: '$pricing.total' },
        averageOrderValue: { $avg: '$pricing.total' },
        totalItems: { $sum: { $sum: '$items.quantity' } }
      }
    }
  ]);
  
  return stats[0] || {
    totalOrders: 0,
    totalRevenue: 0,
    averageOrderValue: 0,
    totalItems: 0
  };
};

// Method to update status
orderSchema.methods.updateStatus = function(newStatus, note, updatedBy) {
  this.status = newStatus;
  this.statusHistory.push({
    status: newStatus,
    note,
    updatedBy,
    updatedAt: new Date()
  });
  
  // Set specific timestamps
  if (newStatus === 'shipped' && !this.shipping.shippedAt) {
    this.shipping.shippedAt = new Date();
  } else if (newStatus === 'delivered' && !this.shipping.deliveredAt) {
    this.shipping.deliveredAt = new Date();
  } else if (newStatus === 'cancelled' && !this.cancellation.cancelledAt) {
    this.cancellation.cancelledAt = new Date();
    this.cancellation.cancelledBy = updatedBy;
  }
  
  return this.save();
};

// Method to calculate refund amount
orderSchema.methods.calculateRefund = function(items = null) {
  if (!items) {
    // Full refund
    return this.pricing.total;
  }
  
  // Partial refund
  let refundAmount = 0;
  items.forEach(refundItem => {
    const orderItem = this.items.find(item => 
      item.product.toString() === refundItem.productId.toString()
    );
    if (orderItem) {
      refundAmount += (orderItem.price * refundItem.quantity);
    }
  });
  
  return refundAmount;
};

module.exports = mongoose.model('Order', orderSchema);
