// Login Page JavaScript

document.addEventListener('DOMContentLoaded', () => {
  console.log('🔐 Login page loaded');

  // Check if user is already logged in
  if (Auth.isLoggedIn()) {
    // Redirect to home page or intended destination
    const urlParams = new URLSearchParams(window.location.search);
    const redirectTo = urlParams.get('redirect') || '/';
    window.location.href = redirectTo;
    return;
  }

  // Initialize login form
  initLoginForm();

  // Initialize password toggle
  initPasswordToggle();

  // Initialize social login buttons
  initSocialLogin();
});

// Initialize login form
function initLoginForm() {
  const form = document.getElementById('loginForm');
  const emailInput = document.getElementById('email');
  const passwordInput = document.getElementById('password');
  const loginBtn = document.getElementById('loginBtn');
  const rememberMeCheckbox = document.getElementById('rememberMe');

  if (!form) return;

  // Load saved email if remember me was checked
  const savedEmail = localStorage.getItem('rememberedEmail');
  if (savedEmail) {
    emailInput.value = savedEmail;
    rememberMeCheckbox.checked = true;
  }

  // Form validation
  form.addEventListener('submit', async (e) => {
    e.preventDefault();
    
    // Clear previous validation
    clearValidation();

    // Get form data
    const formData = new FormData(form);
    const email = formData.get('email').trim();
    const password = formData.get('password');
    const rememberMe = formData.get('rememberMe');

    // Validate inputs
    let isValid = true;

    if (!email) {
      showFieldError('email', 'البريد الإلكتروني مطلوب');
      isValid = false;
    } else if (!isValidEmail(email)) {
      showFieldError('email', 'يرجى إدخال بريد إلكتروني صحيح');
      isValid = false;
    }

    if (!password) {
      showFieldError('password', 'كلمة المرور مطلوبة');
      isValid = false;
    } else if (password.length < 6) {
      showFieldError('password', 'كلمة المرور يجب أن تكون على الأقل 6 أحرف');
      isValid = false;
    }

    if (!isValid) return;

    // Disable form during submission
    setFormLoading(true);

    try {
      // Attempt login
      await Auth.login(email, password);

      // Handle remember me
      if (rememberMe) {
        localStorage.setItem('rememberedEmail', email);
      } else {
        localStorage.removeItem('rememberedEmail');
      }

      // Get redirect URL
      const urlParams = new URLSearchParams(window.location.search);
      const redirectTo = urlParams.get('redirect') || '/';

      // Show success message
      Utils.showToast('تم تسجيل الدخول بنجاح! جاري التوجيه...', 'success');

      // Redirect after short delay
      setTimeout(() => {
        window.location.href = redirectTo;
      }, 1500);

    } catch (error) {
      console.error('Login error:', error);
      
      // Show error message
      if (error.message.includes('البريد الإلكتروني أو كلمة المرور غير صحيحة')) {
        showFieldError('email', 'البريد الإلكتروني أو كلمة المرور غير صحيحة');
        showFieldError('password', '');
      } else if (error.message.includes('إيقاف')) {
        Utils.showToast('تم إيقاف حسابك، يرجى التواصل مع الإدارة', 'danger');
      } else if (error.message.includes('قفل')) {
        Utils.showToast('تم قفل حسابك مؤقتاً بسبب محاولات دخول خاطئة متكررة', 'warning');
      } else {
        Utils.showToast(error.message || 'حدث خطأ في تسجيل الدخول', 'danger');
      }
    } finally {
      setFormLoading(false);
    }
  });

  // Real-time validation
  emailInput.addEventListener('blur', () => {
    const email = emailInput.value.trim();
    if (email && !isValidEmail(email)) {
      showFieldError('email', 'يرجى إدخال بريد إلكتروني صحيح');
    } else {
      clearFieldError('email');
    }
  });

  passwordInput.addEventListener('input', () => {
    const password = passwordInput.value;
    if (password && password.length < 6) {
      showFieldError('password', 'كلمة المرور يجب أن تكون على الأقل 6 أحرف');
    } else {
      clearFieldError('password');
    }
  });
}

// Initialize password toggle functionality
function initPasswordToggle() {
  const toggleBtn = document.getElementById('togglePassword');
  const passwordInput = document.getElementById('password');

  if (!toggleBtn || !passwordInput) return;

  toggleBtn.addEventListener('click', () => {
    const type = passwordInput.getAttribute('type') === 'password' ? 'text' : 'password';
    passwordInput.setAttribute('type', type);
    
    const icon = toggleBtn.querySelector('i');
    if (type === 'password') {
      icon.classList.remove('fa-eye-slash');
      icon.classList.add('fa-eye');
    } else {
      icon.classList.remove('fa-eye');
      icon.classList.add('fa-eye-slash');
    }
  });
}

// Initialize social login buttons
function initSocialLogin() {
  const googleBtn = document.querySelector('.btn-outline-danger');
  const facebookBtn = document.querySelector('.btn-outline-primary');

  if (googleBtn) {
    googleBtn.addEventListener('click', () => {
      Utils.showToast('تسجيل الدخول بـ Google غير متاح حالياً', 'info');
    });
  }

  if (facebookBtn) {
    facebookBtn.addEventListener('click', () => {
      Utils.showToast('تسجيل الدخول بـ Facebook غير متاح حالياً', 'info');
    });
  }
}

// Validation helper functions
function isValidEmail(email) {
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  return emailRegex.test(email);
}

function showFieldError(fieldName, message) {
  const field = document.getElementById(fieldName);
  const feedback = field.parentElement.querySelector('.invalid-feedback');
  
  field.classList.add('is-invalid');
  if (feedback) {
    feedback.textContent = message;
  }
}

function clearFieldError(fieldName) {
  const field = document.getElementById(fieldName);
  const feedback = field.parentElement.querySelector('.invalid-feedback');
  
  field.classList.remove('is-invalid');
  if (feedback) {
    feedback.textContent = '';
  }
}

function clearValidation() {
  const invalidFields = document.querySelectorAll('.is-invalid');
  invalidFields.forEach(field => {
    field.classList.remove('is-invalid');
  });

  const feedbacks = document.querySelectorAll('.invalid-feedback');
  feedbacks.forEach(feedback => {
    feedback.textContent = '';
  });
}

function setFormLoading(loading) {
  const form = document.getElementById('loginForm');
  const loginBtn = document.getElementById('loginBtn');
  const inputs = form.querySelectorAll('input, button');

  if (loading) {
    inputs.forEach(input => input.disabled = true);
    loginBtn.innerHTML = `
      <span class="spinner-border spinner-border-sm me-2" role="status"></span>
      جاري تسجيل الدخول...
    `;
  } else {
    inputs.forEach(input => input.disabled = false);
    loginBtn.innerHTML = `
      <i class="fas fa-sign-in-alt me-2"></i>
      تسجيل الدخول
    `;
  }
}

// Handle Enter key in form
document.addEventListener('keypress', (e) => {
  if (e.key === 'Enter') {
    const form = document.getElementById('loginForm');
    if (form && document.activeElement.form === form) {
      e.preventDefault();
      form.dispatchEvent(new Event('submit'));
    }
  }
});

// Add smooth animations
document.addEventListener('DOMContentLoaded', () => {
  const card = document.querySelector('.card');
  if (card) {
    card.style.opacity = '0';
    card.style.transform = 'translateY(20px)';
    
    setTimeout(() => {
      card.style.transition = 'all 0.5s ease';
      card.style.opacity = '1';
      card.style.transform = 'translateY(0)';
    }, 100);
  }
});
