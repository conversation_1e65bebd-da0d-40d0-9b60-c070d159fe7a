#!/usr/bin/env node

const { spawn } = require('child_process');
const path = require('path');
const fs = require('fs');

// Colors for console output
const colors = {
  reset: '\x1b[0m',
  bright: '\x1b[1m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  magenta: '\x1b[35m',
  cyan: '\x1b[36m'
};

const log = (message, color = 'reset') => {
  console.log(`${colors[color]}${message}${colors.reset}`);
};

const checkFile = (filePath) => {
  return fs.existsSync(path.join(__dirname, '..', filePath));
};

const checkEnvironment = () => {
  log('🔍 Checking environment...', 'cyan');
  
  // Check if .env file exists
  if (!checkFile('.env')) {
    log('⚠️  .env file not found. Creating from .env.example...', 'yellow');
    if (checkFile('.env.example')) {
      fs.copyFileSync(
        path.join(__dirname, '..', '.env.example'),
        path.join(__dirname, '..', '.env')
      );
      log('✅ .env file created. Please update it with your configuration.', 'green');
    } else {
      log('❌ .env.example file not found. Please create .env manually.', 'red');
      process.exit(1);
    }
  }
  
  // Check if node_modules exists
  if (!checkFile('node_modules')) {
    log('⚠️  node_modules not found. Installing dependencies...', 'yellow');
    return installDependencies();
  }
  
  return Promise.resolve();
};

const installDependencies = () => {
  return new Promise((resolve, reject) => {
    log('📦 Installing dependencies...', 'cyan');
    
    const npm = spawn('npm', ['install'], {
      cwd: path.join(__dirname, '..'),
      stdio: 'inherit'
    });
    
    npm.on('close', (code) => {
      if (code === 0) {
        log('✅ Dependencies installed successfully!', 'green');
        resolve();
      } else {
        log('❌ Failed to install dependencies', 'red');
        reject(new Error('npm install failed'));
      }
    });
  });
};

const startServer = () => {
  log('🚀 Starting server...', 'cyan');
  
  const server = spawn('node', ['server.js'], {
    cwd: path.join(__dirname, '..'),
    stdio: 'inherit',
    env: { ...process.env, NODE_ENV: 'development' }
  });
  
  server.on('close', (code) => {
    if (code !== 0) {
      log(`❌ Server exited with code ${code}`, 'red');
    }
  });
  
  // Handle graceful shutdown
  process.on('SIGINT', () => {
    log('\n🛑 Shutting down server...', 'yellow');
    server.kill('SIGINT');
    process.exit(0);
  });
};

const displayInfo = () => {
  const port = process.env.PORT || 3000;
  
  log('\n' + '='.repeat(60), 'cyan');
  log('🎉 Arabic E-commerce Platform', 'bright');
  log('='.repeat(60), 'cyan');
  log(`🌐 Frontend: http://localhost:${port}`, 'green');
  log(`🔧 Admin Panel: http://localhost:${port}/admin`, 'green');
  log(`📚 API Docs: http://localhost:${port}/api-docs`, 'green');
  log('='.repeat(60), 'cyan');
  log('📋 Sample Accounts:', 'yellow');
  log('👤 Admin: <EMAIL> / admin123456', 'white');
  log('👤 User: <EMAIL> / user123456', 'white');
  log('='.repeat(60), 'cyan');
  log('🎫 Sample Coupons:', 'yellow');
  log('🏷️  WELCOME10 - 10% discount for new customers', 'white');
  log('🏷️  SAVE20 - 20 SAR discount on orders over 200 SAR', 'white');
  log('='.repeat(60), 'cyan');
  log('💡 Tips:', 'magenta');
  log('• Run "npm run seed" to add sample data', 'white');
  log('• Press Ctrl+C to stop the server', 'white');
  log('• Check README.md for more information', 'white');
  log('='.repeat(60) + '\n', 'cyan');
};

const main = async () => {
  try {
    log('🌟 Welcome to Arabic E-commerce Platform!', 'bright');
    log('Starting setup and server...', 'cyan');
    
    await checkEnvironment();
    displayInfo();
    startServer();
    
  } catch (error) {
    log(`❌ Error: ${error.message}`, 'red');
    process.exit(1);
  }
};

// Run if this file is executed directly
if (require.main === module) {
  main();
}

module.exports = { main, checkEnvironment, installDependencies, startServer };
