const errorHandler = (err, req, res, next) => {
  let error = { ...err };
  error.message = err.message;

  // Log error
  console.error('Error:', err);

  // Mongoose bad ObjectId
  if (err.name === 'CastError') {
    const message = 'المورد غير موجود';
    const messageEn = 'Resource not found';
    error = {
      message,
      messageEn,
      statusCode: 404
    };
  }

  // Mongoose duplicate key
  if (err.code === 11000) {
    let message = 'البيانات مكررة';
    let messageEn = 'Duplicate field value entered';
    
    // Extract field name from error
    const field = Object.keys(err.keyValue)[0];
    const value = err.keyValue[field];
    
    // Customize message based on field
    switch (field) {
      case 'email':
        message = 'البريد الإلكتروني مستخدم بالفعل';
        messageEn = 'Email already exists';
        break;
      case 'phone':
        message = 'رقم الهاتف مستخدم بالفعل';
        messageEn = 'Phone number already exists';
        break;
      case 'slug':
        message = 'الرابط مستخدم بالفعل';
        messageEn = 'Slug already exists';
        break;
      case 'sku':
        message = 'رمز المنتج مستخدم بالفعل';
        messageEn = 'SKU already exists';
        break;
      case 'code':
        message = 'الكود مستخدم بالفعل';
        messageEn = 'Code already exists';
        break;
      default:
        message = `${field} مستخدم بالفعل`;
        messageEn = `${field} already exists`;
    }
    
    error = {
      message,
      messageEn,
      statusCode: 400,
      field,
      value
    };
  }

  // Mongoose validation error
  if (err.name === 'ValidationError') {
    const messages = [];
    const messagesEn = [];
    const fields = {};
    
    Object.values(err.errors).forEach((val) => {
      messages.push(val.message);
      messagesEn.push(val.message);
      fields[val.path] = val.message;
    });
    
    error = {
      message: messages.join(', '),
      messageEn: messagesEn.join(', '),
      statusCode: 400,
      fields
    };
  }

  // JWT errors
  if (err.name === 'JsonWebTokenError') {
    const message = 'رمز المصادقة غير صحيح';
    const messageEn = 'Invalid token';
    error = {
      message,
      messageEn,
      statusCode: 401
    };
  }

  if (err.name === 'TokenExpiredError') {
    const message = 'انتهت صلاحية رمز المصادقة';
    const messageEn = 'Token expired';
    error = {
      message,
      messageEn,
      statusCode: 401
    };
  }

  // Multer errors (file upload)
  if (err.code === 'LIMIT_FILE_SIZE') {
    const message = 'حجم الملف كبير جداً';
    const messageEn = 'File too large';
    error = {
      message,
      messageEn,
      statusCode: 400
    };
  }

  if (err.code === 'LIMIT_FILE_COUNT') {
    const message = 'عدد الملفات كبير جداً';
    const messageEn = 'Too many files';
    error = {
      message,
      messageEn,
      statusCode: 400
    };
  }

  if (err.code === 'LIMIT_UNEXPECTED_FILE') {
    const message = 'نوع الملف غير مدعوم';
    const messageEn = 'Unexpected file type';
    error = {
      message,
      messageEn,
      statusCode: 400
    };
  }

  // MongoDB connection errors
  if (err.name === 'MongoNetworkError' || err.name === 'MongoTimeoutError') {
    const message = 'خطأ في الاتصال بقاعدة البيانات';
    const messageEn = 'Database connection error';
    error = {
      message,
      messageEn,
      statusCode: 503
    };
  }

  // Payment errors
  if (err.type === 'StripeCardError') {
    const message = 'خطأ في بيانات البطاقة';
    const messageEn = 'Card error';
    error = {
      message,
      messageEn,
      statusCode: 400,
      paymentError: true
    };
  }

  // Rate limiting errors
  if (err.status === 429) {
    const message = 'تم تجاوز الحد المسموح من الطلبات';
    const messageEn = 'Too many requests';
    error = {
      message,
      messageEn,
      statusCode: 429
    };
  }

  // Custom application errors
  if (err.isOperational) {
    error = {
      message: err.message,
      messageEn: err.messageEn || err.message,
      statusCode: err.statusCode || 500,
      ...err.data
    };
  }

  // Default error response
  const statusCode = error.statusCode || 500;
  const message = error.message || 'خطأ في الخادم';
  const messageEn = error.messageEn || 'Server Error';

  // Prepare error response
  const errorResponse = {
    success: false,
    message,
    messageEn,
    ...(process.env.NODE_ENV === 'development' && {
      stack: err.stack,
      originalError: err
    })
  };

  // Add additional error data if available
  if (error.fields) errorResponse.fields = error.fields;
  if (error.field) errorResponse.field = error.field;
  if (error.value) errorResponse.value = error.value;
  if (error.paymentError) errorResponse.paymentError = true;

  // Log error details in production
  if (process.env.NODE_ENV === 'production') {
    console.error('Production Error:', {
      message: err.message,
      stack: err.stack,
      url: req.originalUrl,
      method: req.method,
      ip: req.ip,
      userAgent: req.get('User-Agent'),
      user: req.user ? req.user._id : 'anonymous',
      timestamp: new Date().toISOString()
    });
  }

  res.status(statusCode).json(errorResponse);
};

// Custom error class for operational errors
class AppError extends Error {
  constructor(message, statusCode, messageEn = null, data = {}) {
    super(message);
    this.statusCode = statusCode;
    this.messageEn = messageEn;
    this.data = data;
    this.isOperational = true;

    Error.captureStackTrace(this, this.constructor);
  }
}

// Async error handler wrapper
const asyncHandler = (fn) => (req, res, next) => {
  Promise.resolve(fn(req, res, next)).catch(next);
};

// Not found middleware
const notFound = (req, res, next) => {
  const error = new AppError(
    `المسار ${req.originalUrl} غير موجود`,
    404,
    `Route ${req.originalUrl} not found`
  );
  next(error);
};

module.exports = {
  errorHandler,
  AppError,
  asyncHandler,
  notFound
};
