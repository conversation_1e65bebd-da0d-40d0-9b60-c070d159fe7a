# 🚀 دليل البدء السريع | Quick Start Guide

## ⚡ التشغيل في 3 خطوات

### الخطوة 1: تحقق من النظام
```bash
# فحص المتطلبات الأساسية
node check-system.js
```

### الخطوة 2: تشغيل الإعداد التلقائي
```bash
# Windows - Command Prompt
start.bat

# Windows - PowerShell  
.\start.ps1

# Linux/Mac/Windows (مع Node.js)
npm run setup
```

### الخطوة 3: افتح المتصفح
- **الموقع:** http://localhost:3000
- **لوحة التحكم:** http://localhost:3000/admin

---

## 🎯 إذا لم يعمل التشغيل التلقائي

### تثبيت Node.js
1. اذهب إلى [nodejs.org](https://nodejs.org/)
2. حمل النسخة LTS
3. ثبتها مع الإعدادات الافتراضية
4. أعد تشغيل Command Prompt

### تثبيت MongoDB
**الخيار الأول - محلي:**
1. اذهب إلى [mongodb.com/try/download/community](https://www.mongodb.com/try/download/community)
2. حمل MongoDB Community Server
3. ثبته مع الإعدادات الافتراضية

**الخيار الثاني - سحابي (أسهل):**
1. اذهب إلى [mongodb.com/atlas](https://www.mongodb.com/atlas)
2. أنشئ حساب مجاني
3. أنشئ cluster جديد
4. احصل على connection string
5. ضعه في ملف `.env`:
   ```
   MONGODB_URI=mongodb+srv://username:<EMAIL>/arabic_ecommerce
   ```

### التشغيل اليدوي
```bash
# 1. تثبيت التبعيات
npm install

# 2. إنشاء ملف البيئة
copy .env.example .env

# 3. إضافة بيانات تجريبية
npm run seed

# 4. تشغيل الخادم
npm start
```

---

## 🔐 الحسابات التجريبية

### حساب المدير
- **البريد:** <EMAIL>
- **كلمة المرور:** admin123456
- **الصلاحيات:** إدارة كاملة

### حساب المستخدم
- **البريد:** <EMAIL>  
- **كلمة المرور:** user123456
- **الصلاحيات:** تسوق عادي

---

## 🌐 الصفحات المهمة

| الصفحة | الرابط | الوصف |
|---------|---------|--------|
| الرئيسية | http://localhost:3000 | الموقع الأساسي |
| المنتجات | http://localhost:3000/products | عرض المنتجات |
| تسجيل الدخول | http://localhost:3000/login | دخول المستخدمين |
| لوحة التحكم | http://localhost:3000/admin | إدارة المتجر |
| توثيق API | http://localhost:3000/api-docs | وثائق البرمجة |

---

## 🛠️ أوامر مفيدة

```bash
# فحص النظام
npm run check

# إعداد سريع
npm run setup

# تشغيل للتطوير
npm run dev

# إضافة بيانات تجريبية
npm run seed

# تشغيل الاختبارات
npm test

# فحص الكود
npm run lint
```

---

## ❌ حل المشاكل الشائعة

### "npm is not recognized"
**المشكلة:** Node.js غير مثبت
**الحل:** ثبت Node.js من [nodejs.org](https://nodejs.org/)

### "connect ECONNREFUSED 127.0.0.1:27017"
**المشكلة:** MongoDB غير مشغل
**الحل:** 
- شغل MongoDB: `mongod`
- أو استخدم MongoDB Atlas

### "Port 3000 is already in use"
**المشكلة:** المنفذ مستخدم
**الحل:** غير المنفذ في `.env`:
```
PORT=3001
```

### "Cannot find module"
**المشكلة:** التبعيات غير مثبتة
**الحل:** 
```bash
rm -rf node_modules
npm install
```

---

## 📞 الحصول على المساعدة

### 📖 الوثائق
- [README.md](README.md) - دليل شامل
- [SETUP_GUIDE.md](SETUP_GUIDE.md) - دليل الإعداد المفصل
- [CONTRIBUTING.md](CONTRIBUTING.md) - دليل المساهمة

### 🌐 الدعم
- [GitHub Issues](https://github.com/your-username/arabic-ecommerce-platform/issues)
- [GitHub Discussions](https://github.com/your-username/arabic-ecommerce-platform/discussions)

### 💡 نصائح
- استخدم VS Code كمحرر نصوص
- ثبت MongoDB Compass لإدارة قاعدة البيانات
- استخدم Postman لاختبار APIs
- فعّل Developer Tools في المتصفح

---

## 🎉 مبروك!

إذا وصلت هنا، فقد نجحت في تشغيل المشروع! 

الآن يمكنك:
- ✅ تصفح المتجر
- ✅ إضافة منتجات جديدة
- ✅ إدارة الطلبات
- ✅ تخصيص التصميم
- ✅ تطوير ميزات جديدة

**استمتع بالتطوير!** 🚀
