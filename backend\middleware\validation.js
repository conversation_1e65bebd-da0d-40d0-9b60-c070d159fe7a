const { body, param, query, validationResult } = require('express-validator');
const { AppError } = require('./errorHandler');

// Handle validation errors
const handleValidationErrors = (req, res, next) => {
  const errors = validationResult(req);
  
  if (!errors.isEmpty()) {
    const errorMessages = [];
    const errorMessagesEn = [];
    const fields = {};
    
    errors.array().forEach(error => {
      errorMessages.push(error.msg);
      errorMessagesEn.push(error.msg);
      fields[error.param] = error.msg;
    });
    
    return res.status(400).json({
      success: false,
      message: errorMessages.join(', '),
      messageEn: errorMessagesEn.join(', '),
      fields
    });
  }
  
  next();
};

// User validation rules
const validateUserRegistration = [
  body('name')
    .trim()
    .isLength({ min: 2, max: 50 })
    .withMessage('الاسم يجب أن يكون بين 2 و 50 حرف'),
  
  body('email')
    .isEmail()
    .normalizeEmail()
    .withMessage('يرجى إدخال بريد إلكتروني صحيح'),
  
  body('password')
    .isLength({ min: 6 })
    .withMessage('كلمة المرور يجب أن تكون على الأقل 6 أحرف')
    .matches(/^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)/)
    .withMessage('كلمة المرور يجب أن تحتوي على حرف كبير وصغير ورقم'),
  
  body('phone')
    .isMobilePhone(['ar-SA', 'ar-EG', 'ar-AE'])
    .withMessage('يرجى إدخال رقم هاتف صحيح'),
  
  handleValidationErrors
];

const validateUserLogin = [
  body('email')
    .isEmail()
    .normalizeEmail()
    .withMessage('يرجى إدخال بريد إلكتروني صحيح'),
  
  body('password')
    .notEmpty()
    .withMessage('كلمة المرور مطلوبة'),
  
  handleValidationErrors
];

const validateUserUpdate = [
  body('name')
    .optional()
    .trim()
    .isLength({ min: 2, max: 50 })
    .withMessage('الاسم يجب أن يكون بين 2 و 50 حرف'),
  
  body('phone')
    .optional()
    .isMobilePhone(['ar-SA', 'ar-EG', 'ar-AE'])
    .withMessage('يرجى إدخال رقم هاتف صحيح'),
  
  handleValidationErrors
];

const validatePasswordChange = [
  body('currentPassword')
    .notEmpty()
    .withMessage('كلمة المرور الحالية مطلوبة'),
  
  body('newPassword')
    .isLength({ min: 6 })
    .withMessage('كلمة المرور الجديدة يجب أن تكون على الأقل 6 أحرف')
    .matches(/^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)/)
    .withMessage('كلمة المرور يجب أن تحتوي على حرف كبير وصغير ورقم'),
  
  handleValidationErrors
];

// Product validation rules
const validateProduct = [
  body('name.ar')
    .trim()
    .isLength({ min: 2, max: 100 })
    .withMessage('اسم المنتج باللغة العربية يجب أن يكون بين 2 و 100 حرف'),
  
  body('description.ar')
    .trim()
    .isLength({ min: 10, max: 2000 })
    .withMessage('وصف المنتج باللغة العربية يجب أن يكون بين 10 و 2000 حرف'),
  
  body('price')
    .isFloat({ min: 0 })
    .withMessage('السعر يجب أن يكون رقم موجب'),
  
  body('category')
    .isMongoId()
    .withMessage('معرف الفئة غير صحيح'),
  
  body('stock.quantity')
    .isInt({ min: 0 })
    .withMessage('كمية المخزون يجب أن تكون رقم صحيح موجب'),
  
  handleValidationErrors
];

const validateProductUpdate = [
  body('name.ar')
    .optional()
    .trim()
    .isLength({ min: 2, max: 100 })
    .withMessage('اسم المنتج باللغة العربية يجب أن يكون بين 2 و 100 حرف'),
  
  body('description.ar')
    .optional()
    .trim()
    .isLength({ min: 10, max: 2000 })
    .withMessage('وصف المنتج باللغة العربية يجب أن يكون بين 10 و 2000 حرف'),
  
  body('price')
    .optional()
    .isFloat({ min: 0 })
    .withMessage('السعر يجب أن يكون رقم موجب'),
  
  body('category')
    .optional()
    .isMongoId()
    .withMessage('معرف الفئة غير صحيح'),
  
  body('stock.quantity')
    .optional()
    .isInt({ min: 0 })
    .withMessage('كمية المخزون يجب أن تكون رقم صحيح موجب'),
  
  handleValidationErrors
];

// Category validation rules
const validateCategory = [
  body('name.ar')
    .trim()
    .isLength({ min: 2, max: 50 })
    .withMessage('اسم الفئة باللغة العربية يجب أن يكون بين 2 و 50 حرف'),
  
  body('parent')
    .optional()
    .isMongoId()
    .withMessage('معرف الفئة الأب غير صحيح'),
  
  handleValidationErrors
];

// Order validation rules
const validateOrder = [
  body('items')
    .isArray({ min: 1 })
    .withMessage('يجب أن يحتوي الطلب على منتج واحد على الأقل'),
  
  body('items.*.product')
    .isMongoId()
    .withMessage('معرف المنتج غير صحيح'),
  
  body('items.*.quantity')
    .isInt({ min: 1 })
    .withMessage('الكمية يجب أن تكون رقم صحيح موجب'),
  
  body('shippingAddress.name')
    .trim()
    .isLength({ min: 2, max: 50 })
    .withMessage('اسم المستلم يجب أن يكون بين 2 و 50 حرف'),
  
  body('shippingAddress.phone')
    .isMobilePhone(['ar-SA', 'ar-EG', 'ar-AE'])
    .withMessage('يرجى إدخال رقم هاتف صحيح'),
  
  body('shippingAddress.street')
    .trim()
    .isLength({ min: 5, max: 200 })
    .withMessage('عنوان الشارع يجب أن يكون بين 5 و 200 حرف'),
  
  body('shippingAddress.city')
    .trim()
    .isLength({ min: 2, max: 50 })
    .withMessage('المدينة يجب أن تكون بين 2 و 50 حرف'),
  
  body('payment.method')
    .isIn(['cash_on_delivery', 'credit_card', 'bank_transfer', 'wallet'])
    .withMessage('طريقة الدفع غير صحيحة'),
  
  handleValidationErrors
];

// Review validation rules
const validateReview = [
  body('rating')
    .isInt({ min: 1, max: 5 })
    .withMessage('التقييم يجب أن يكون بين 1 و 5'),
  
  body('comment')
    .trim()
    .isLength({ min: 10, max: 1000 })
    .withMessage('التعليق يجب أن يكون بين 10 و 1000 حرف'),
  
  body('title')
    .optional()
    .trim()
    .isLength({ max: 100 })
    .withMessage('العنوان يجب أن يكون أقل من 100 حرف'),
  
  handleValidationErrors
];

// Coupon validation rules
const validateCoupon = [
  body('code')
    .trim()
    .isLength({ min: 3, max: 20 })
    .isAlphanumeric()
    .withMessage('كود الكوبون يجب أن يكون بين 3 و 20 حرف ويحتوي على أحرف وأرقام فقط'),
  
  body('name.ar')
    .trim()
    .isLength({ min: 2, max: 50 })
    .withMessage('اسم الكوبون باللغة العربية يجب أن يكون بين 2 و 50 حرف'),
  
  body('type')
    .isIn(['percentage', 'fixed', 'free_shipping'])
    .withMessage('نوع الكوبون غير صحيح'),
  
  body('value')
    .isFloat({ min: 0 })
    .withMessage('قيمة الكوبون يجب أن تكون رقم موجب'),
  
  body('validFrom')
    .isISO8601()
    .withMessage('تاريخ بداية الصلاحية غير صحيح'),
  
  body('validUntil')
    .isISO8601()
    .withMessage('تاريخ انتهاء الصلاحية غير صحيح'),
  
  handleValidationErrors
];

// Parameter validation
const validateObjectId = (paramName) => [
  param(paramName)
    .isMongoId()
    .withMessage(`معرف ${paramName} غير صحيح`),
  
  handleValidationErrors
];

// Query validation
const validatePagination = [
  query('page')
    .optional()
    .isInt({ min: 1 })
    .withMessage('رقم الصفحة يجب أن يكون رقم صحيح موجب'),
  
  query('limit')
    .optional()
    .isInt({ min: 1, max: 100 })
    .withMessage('حد النتائج يجب أن يكون بين 1 و 100'),
  
  handleValidationErrors
];

const validateSearch = [
  query('q')
    .optional()
    .trim()
    .isLength({ min: 1, max: 100 })
    .withMessage('كلمة البحث يجب أن تكون بين 1 و 100 حرف'),
  
  handleValidationErrors
];

// File validation
const validateFileUpload = (req, res, next) => {
  if (!req.file && !req.files) {
    return res.status(400).json({
      success: false,
      message: 'لم يتم رفع أي ملف',
      messageEn: 'No file uploaded'
    });
  }
  
  const file = req.file || (req.files && req.files[0]);
  
  // Check file type
  const allowedTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/webp'];
  if (!allowedTypes.includes(file.mimetype)) {
    return res.status(400).json({
      success: false,
      message: 'نوع الملف غير مدعوم. الأنواع المدعومة: JPEG, PNG, WebP',
      messageEn: 'File type not supported. Supported types: JPEG, PNG, WebP'
    });
  }
  
  // Check file size (5MB max)
  const maxSize = 5 * 1024 * 1024; // 5MB
  if (file.size > maxSize) {
    return res.status(400).json({
      success: false,
      message: 'حجم الملف كبير جداً. الحد الأقصى 5 ميجابايت',
      messageEn: 'File too large. Maximum size is 5MB'
    });
  }
  
  next();
};

module.exports = {
  handleValidationErrors,
  validateUserRegistration,
  validateUserLogin,
  validateUserUpdate,
  validatePasswordChange,
  validateProduct,
  validateProductUpdate,
  validateCategory,
  validateOrder,
  validateReview,
  validateCoupon,
  validateObjectId,
  validatePagination,
  validateSearch,
  validateFileUpload
};
