const multer = require('multer');
const sharp = require('sharp');
const path = require('path');
const fs = require('fs').promises;
const { AppError } = require('./errorHandler');

// Ensure upload directories exist
const ensureDirectoryExists = async (dirPath) => {
  try {
    await fs.access(dirPath);
  } catch (error) {
    await fs.mkdir(dirPath, { recursive: true });
  }
};

// Configure multer storage
const storage = multer.memoryStorage();

// File filter function
const fileFilter = (req, file, cb) => {
  // Check file type
  const allowedTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/webp'];
  
  if (allowedTypes.includes(file.mimetype)) {
    cb(null, true);
  } else {
    cb(new AppError(
      'نوع الملف غير مدعوم. الأنواع المدعومة: JPEG, PNG, WebP',
      400,
      'File type not supported. Supported types: JPEG, PNG, WebP'
    ), false);
  }
};

// Configure multer
const upload = multer({
  storage,
  fileFilter,
  limits: {
    fileSize: parseInt(process.env.MAX_FILE_SIZE) || 5 * 1024 * 1024, // 5MB default
    files: 10 // Maximum 10 files
  }
});

// Image processing function
const processImage = async (buffer, options = {}) => {
  const {
    width = 800,
    height = 600,
    quality = 80,
    format = 'webp',
    fit = 'cover'
  } = options;

  try {
    const processedBuffer = await sharp(buffer)
      .resize(width, height, { fit })
      .toFormat(format, { quality })
      .toBuffer();

    return processedBuffer;
  } catch (error) {
    throw new AppError(
      'خطأ في معالجة الصورة',
      500,
      'Error processing image'
    );
  }
};

// Generate unique filename
const generateFilename = (originalName, prefix = '') => {
  const timestamp = Date.now();
  const random = Math.round(Math.random() * 1E9);
  const ext = path.extname(originalName).toLowerCase();
  return `${prefix}${timestamp}-${random}${ext}`;
};

// Save processed image
const saveImage = async (buffer, filename, directory = 'general') => {
  const uploadPath = path.join(process.env.UPLOAD_PATH || './public/uploads', directory);
  await ensureDirectoryExists(uploadPath);
  
  const filePath = path.join(uploadPath, filename);
  await fs.writeFile(filePath, buffer);
  
  return `/uploads/${directory}/${filename}`;
};

// Middleware for single image upload
const uploadSingle = (fieldName, options = {}) => {
  return async (req, res, next) => {
    const uploadMiddleware = upload.single(fieldName);
    
    uploadMiddleware(req, res, async (err) => {
      if (err) {
        if (err instanceof multer.MulterError) {
          if (err.code === 'LIMIT_FILE_SIZE') {
            return next(new AppError(
              'حجم الملف كبير جداً',
              400,
              'File too large'
            ));
          }
          if (err.code === 'LIMIT_UNEXPECTED_FILE') {
            return next(new AppError(
              'حقل الملف غير متوقع',
              400,
              'Unexpected file field'
            ));
          }
        }
        return next(err);
      }

      if (!req.file) {
        return next();
      }

      try {
        // Process image
        const processedBuffer = await processImage(req.file.buffer, options);
        
        // Generate filename
        const filename = generateFilename(
          req.file.originalname,
          options.prefix || ''
        );
        
        // Save image
        const imageUrl = await saveImage(
          processedBuffer,
          filename,
          options.directory || 'general'
        );
        
        // Add image info to request
        req.uploadedImage = {
          url: imageUrl,
          originalName: req.file.originalname,
          size: processedBuffer.length,
          mimetype: `image/${options.format || 'webp'}`
        };
        
        next();
      } catch (error) {
        next(error);
      }
    });
  };
};

// Middleware for multiple images upload
const uploadMultiple = (fieldName, maxCount = 5, options = {}) => {
  return async (req, res, next) => {
    const uploadMiddleware = upload.array(fieldName, maxCount);
    
    uploadMiddleware(req, res, async (err) => {
      if (err) {
        if (err instanceof multer.MulterError) {
          if (err.code === 'LIMIT_FILE_SIZE') {
            return next(new AppError(
              'حجم الملف كبير جداً',
              400,
              'File too large'
            ));
          }
          if (err.code === 'LIMIT_FILE_COUNT') {
            return next(new AppError(
              `عدد الملفات كبير جداً. الحد الأقصى ${maxCount}`,
              400,
              `Too many files. Maximum ${maxCount}`
            ));
          }
        }
        return next(err);
      }

      if (!req.files || req.files.length === 0) {
        return next();
      }

      try {
        const uploadedImages = [];
        
        for (const file of req.files) {
          // Process image
          const processedBuffer = await processImage(file.buffer, options);
          
          // Generate filename
          const filename = generateFilename(
            file.originalname,
            options.prefix || ''
          );
          
          // Save image
          const imageUrl = await saveImage(
            processedBuffer,
            filename,
            options.directory || 'general'
          );
          
          uploadedImages.push({
            url: imageUrl,
            originalName: file.originalname,
            size: processedBuffer.length,
            mimetype: `image/${options.format || 'webp'}`
          });
        }
        
        // Add images info to request
        req.uploadedImages = uploadedImages;
        
        next();
      } catch (error) {
        next(error);
      }
    });
  };
};

// Middleware for product images
const uploadProductImages = uploadMultiple('images', 10, {
  directory: 'products',
  prefix: 'product-',
  width: 800,
  height: 800,
  quality: 85
});

// Middleware for category image
const uploadCategoryImage = uploadSingle('image', {
  directory: 'categories',
  prefix: 'category-',
  width: 400,
  height: 400,
  quality: 80
});

// Middleware for user avatar
const uploadUserAvatar = uploadSingle('avatar', {
  directory: 'avatars',
  prefix: 'avatar-',
  width: 200,
  height: 200,
  quality: 80,
  fit: 'cover'
});

// Middleware for review images
const uploadReviewImages = uploadMultiple('images', 5, {
  directory: 'reviews',
  prefix: 'review-',
  width: 600,
  height: 600,
  quality: 80
});

// Delete image function
const deleteImage = async (imagePath) => {
  try {
    const fullPath = path.join(process.cwd(), 'public', imagePath);
    await fs.unlink(fullPath);
    return true;
  } catch (error) {
    console.error('Error deleting image:', error);
    return false;
  }
};

// Cleanup old images function
const cleanupOldImages = async (directory, daysOld = 30) => {
  try {
    const uploadPath = path.join(process.env.UPLOAD_PATH || './public/uploads', directory);
    const files = await fs.readdir(uploadPath);
    const cutoffDate = new Date();
    cutoffDate.setDate(cutoffDate.getDate() - daysOld);
    
    for (const file of files) {
      const filePath = path.join(uploadPath, file);
      const stats = await fs.stat(filePath);
      
      if (stats.mtime < cutoffDate) {
        await fs.unlink(filePath);
        console.log(`Deleted old image: ${file}`);
      }
    }
  } catch (error) {
    console.error('Error cleaning up old images:', error);
  }
};

// Generate thumbnail
const generateThumbnail = async (imagePath, options = {}) => {
  try {
    const {
      width = 150,
      height = 150,
      quality = 70
    } = options;
    
    const inputPath = path.join(process.cwd(), 'public', imagePath);
    const outputPath = inputPath.replace(/(\.[^.]+)$/, `-thumb$1`);
    
    await sharp(inputPath)
      .resize(width, height, { fit: 'cover' })
      .jpeg({ quality })
      .toFile(outputPath);
    
    return imagePath.replace(/(\.[^.]+)$/, `-thumb$1`);
  } catch (error) {
    console.error('Error generating thumbnail:', error);
    return null;
  }
};

module.exports = {
  upload,
  uploadSingle,
  uploadMultiple,
  uploadProductImages,
  uploadCategoryImage,
  uploadUserAvatar,
  uploadReviewImages,
  processImage,
  saveImage,
  deleteImage,
  cleanupOldImages,
  generateThumbnail,
  generateFilename
};
