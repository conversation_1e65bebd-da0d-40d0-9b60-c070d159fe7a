const express = require('express');
const {
  getCategories,
  getCategory,
  createCategory,
  updateCategory,
  deleteCategory,
  getCategoryTree,
  getFeaturedCategories,
  searchCategories,
  getCategoryPath,
  updateSortOrder
} = require('../controllers/categoryController');

const { protect, authorize } = require('../middleware/auth');
const { uploadCategoryImage } = require('../middleware/upload');
const {
  validateCategory,
  validateObjectId,
  validatePagination,
  validateSearch
} = require('../middleware/validation');

const router = express.Router();

/**
 * @swagger
 * components:
 *   schemas:
 *     Category:
 *       type: object
 *       required:
 *         - name
 *       properties:
 *         name:
 *           type: object
 *           properties:
 *             ar:
 *               type: string
 *               description: اسم الفئة بالعربية
 *             en:
 *               type: string
 *               description: اسم الفئة بالإنجليزية
 *         description:
 *           type: object
 *           properties:
 *             ar:
 *               type: string
 *               description: وصف الفئة بالعربية
 *             en:
 *               type: string
 *               description: وصف الفئة بالإنجليزية
 *         parent:
 *           type: string
 *           description: معرف الفئة الأب
 *         level:
 *           type: number
 *           description: مستوى الفئة في الهيكل
 *         image:
 *           type: object
 *           properties:
 *             url:
 *               type: string
 *               description: رابط الصورة
 *             alt:
 *               type: object
 *               properties:
 *                 ar:
 *                   type: string
 *                 en:
 *                   type: string
 *         status:
 *           type: string
 *           enum: [active, inactive]
 *           description: حالة الفئة
 *         featured:
 *           type: boolean
 *           description: فئة مميزة
 *         sortOrder:
 *           type: number
 *           description: ترتيب الفئة
 */

/**
 * @swagger
 * /api/categories:
 *   get:
 *     summary: الحصول على قائمة الفئات
 *     tags: [Categories]
 *     parameters:
 *       - in: query
 *         name: tree
 *         schema:
 *           type: boolean
 *         description: إرجاع الفئات في شكل شجرة
 *       - in: query
 *         name: page
 *         schema:
 *           type: integer
 *           minimum: 1
 *         description: رقم الصفحة
 *       - in: query
 *         name: limit
 *         schema:
 *           type: integer
 *           minimum: 1
 *           maximum: 100
 *         description: عدد النتائج في الصفحة
 *       - in: query
 *         name: parent
 *         schema:
 *           type: string
 *         description: معرف الفئة الأب
 *       - in: query
 *         name: level
 *         schema:
 *           type: integer
 *         description: مستوى الفئة
 *       - in: query
 *         name: featured
 *         schema:
 *           type: boolean
 *         description: الفئات المميزة فقط
 *       - in: query
 *         name: populate
 *         schema:
 *           type: string
 *         description: الحقول المطلوب تحميلها (parent,children)
 *     responses:
 *       200:
 *         description: قائمة الفئات
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                 count:
 *                   type: integer
 *                 total:
 *                   type: integer
 *                 pagination:
 *                   type: object
 *                 data:
 *                   type: array
 *                   items:
 *                     $ref: '#/components/schemas/Category'
 */
router.get('/', validatePagination, getCategories);

/**
 * @swagger
 * /api/categories/tree:
 *   get:
 *     summary: الحصول على شجرة الفئات
 *     tags: [Categories]
 *     parameters:
 *       - in: query
 *         name: maxLevel
 *         schema:
 *           type: integer
 *         description: الحد الأقصى لمستوى الفئات
 *     responses:
 *       200:
 *         description: شجرة الفئات
 */
router.get('/tree', getCategoryTree);

/**
 * @swagger
 * /api/categories/featured:
 *   get:
 *     summary: الحصول على الفئات المميزة
 *     tags: [Categories]
 *     parameters:
 *       - in: query
 *         name: limit
 *         schema:
 *           type: integer
 *           minimum: 1
 *           maximum: 50
 *         description: عدد الفئات المطلوبة
 *     responses:
 *       200:
 *         description: الفئات المميزة
 */
router.get('/featured', getFeaturedCategories);

/**
 * @swagger
 * /api/categories/search:
 *   get:
 *     summary: البحث في الفئات
 *     tags: [Categories]
 *     parameters:
 *       - in: query
 *         name: q
 *         required: true
 *         schema:
 *           type: string
 *         description: كلمة البحث
 *     responses:
 *       200:
 *         description: نتائج البحث
 *       400:
 *         description: كلمة البحث مطلوبة
 */
router.get('/search', validateSearch, searchCategories);

/**
 * @swagger
 * /api/categories/{id}:
 *   get:
 *     summary: الحصول على فئة واحدة
 *     tags: [Categories]
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *         description: معرف الفئة
 *     responses:
 *       200:
 *         description: تفاصيل الفئة
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                 data:
 *                   allOf:
 *                     - $ref: '#/components/schemas/Category'
 *                     - type: object
 *                       properties:
 *                         breadcrumb:
 *                           type: array
 *                           items:
 *                             type: object
 *                             properties:
 *                               _id:
 *                                 type: string
 *                               name:
 *                                 type: object
 *                               slug:
 *                                 type: string
 *       404:
 *         description: الفئة غير موجودة
 */
router.get('/:id', validateObjectId('id'), getCategory);

/**
 * @swagger
 * /api/categories/{id}/path:
 *   get:
 *     summary: الحصول على مسار الفئة (breadcrumb)
 *     tags: [Categories]
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *         description: معرف الفئة
 *     responses:
 *       200:
 *         description: مسار الفئة
 *       404:
 *         description: الفئة غير موجودة
 */
router.get('/:id/path', validateObjectId('id'), getCategoryPath);

/**
 * @swagger
 * /api/categories:
 *   post:
 *     summary: إنشاء فئة جديدة
 *     tags: [Categories]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         multipart/form-data:
 *           schema:
 *             type: object
 *             required:
 *               - name[ar]
 *             properties:
 *               name[ar]:
 *                 type: string
 *                 description: اسم الفئة بالعربية
 *               name[en]:
 *                 type: string
 *                 description: اسم الفئة بالإنجليزية
 *               description[ar]:
 *                 type: string
 *                 description: وصف الفئة بالعربية
 *               description[en]:
 *                 type: string
 *                 description: وصف الفئة بالإنجليزية
 *               parent:
 *                 type: string
 *                 description: معرف الفئة الأب
 *               icon:
 *                 type: string
 *                 description: أيقونة الفئة
 *               color:
 *                 type: string
 *                 description: لون الفئة
 *               featured:
 *                 type: boolean
 *                 description: فئة مميزة
 *               sortOrder:
 *                 type: number
 *                 description: ترتيب الفئة
 *               image:
 *                 type: string
 *                 format: binary
 *                 description: صورة الفئة
 *     responses:
 *       201:
 *         description: تم إنشاء الفئة بنجاح
 *       400:
 *         description: خطأ في البيانات
 *       401:
 *         description: غير مصرح
 *       403:
 *         description: ليس لديك صلاحية
 */
router.post('/', protect, authorize('admin', 'manager'), uploadCategoryImage, validateCategory, createCategory);

/**
 * @swagger
 * /api/categories/{id}:
 *   put:
 *     summary: تحديث فئة
 *     tags: [Categories]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *         description: معرف الفئة
 *     requestBody:
 *       required: true
 *       content:
 *         multipart/form-data:
 *           schema:
 *             type: object
 *             properties:
 *               name[ar]:
 *                 type: string
 *               name[en]:
 *                 type: string
 *               description[ar]:
 *                 type: string
 *               description[en]:
 *                 type: string
 *               parent:
 *                 type: string
 *               icon:
 *                 type: string
 *               color:
 *                 type: string
 *               featured:
 *                 type: boolean
 *               sortOrder:
 *                 type: number
 *               image:
 *                 type: string
 *                 format: binary
 *     responses:
 *       200:
 *         description: تم تحديث الفئة بنجاح
 *       404:
 *         description: الفئة غير موجودة
 *       401:
 *         description: غير مصرح
 */
router.put('/:id', protect, authorize('admin', 'manager'), validateObjectId('id'), uploadCategoryImage, updateCategory);

/**
 * @swagger
 * /api/categories/sort:
 *   put:
 *     summary: تحديث ترتيب الفئات
 *     tags: [Categories]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - categories
 *             properties:
 *               categories:
 *                 type: array
 *                 items:
 *                   type: object
 *                   properties:
 *                     id:
 *                       type: string
 *                     sortOrder:
 *                       type: number
 *     responses:
 *       200:
 *         description: تم تحديث ترتيب الفئات بنجاح
 *       400:
 *         description: خطأ في البيانات
 *       401:
 *         description: غير مصرح
 */
router.put('/sort', protect, authorize('admin', 'manager'), updateSortOrder);

/**
 * @swagger
 * /api/categories/{id}:
 *   delete:
 *     summary: حذف فئة
 *     tags: [Categories]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *         description: معرف الفئة
 *     responses:
 *       200:
 *         description: تم حذف الفئة بنجاح
 *       400:
 *         description: لا يمكن حذف فئة تحتوي على فئات فرعية أو منتجات
 *       404:
 *         description: الفئة غير موجودة
 *       401:
 *         description: غير مصرح
 */
router.delete('/:id', protect, authorize('admin'), validateObjectId('id'), deleteCategory);

module.exports = router;
