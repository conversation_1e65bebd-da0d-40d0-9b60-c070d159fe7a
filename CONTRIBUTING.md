# دليل المساهمة | Contributing Guide

نرحب بمساهماتكم في تطوير منصة التجارة الإلكترونية العربية! 🎉

## 📋 جدول المحتويات

- [كيفية المساهمة](#كيفية-المساهمة)
- [الإبلاغ عن الأخطاء](#الإبلاغ-عن-الأخطاء)
- [اقتراح مميزات جديدة](#اقتراح-مميزات-جديدة)
- [إرشادات التطوير](#إرشادات-التطوير)
- [معايير الكود](#معايير-الكود)
- [عملية المراجعة](#عملية-المراجعة)
- [مجتمع المطورين](#مجتمع-المطورين)

## 🤝 كيفية المساهمة

### 1. Fork المشروع
```bash
# انقر على زر Fork في GitHub
# ثم استنسخ المشروع المنسوخ
git clone https://github.com/your-username/arabic-ecommerce-platform.git
cd arabic-ecommerce-platform
```

### 2. إعداد البيئة التطويرية
```bash
# تثبيت التبعيات
npm install

# إعداد ملف البيئة
cp .env.example .env

# تشغيل الإعداد السريع
npm run setup
```

### 3. إنشاء فرع جديد
```bash
# إنشاء فرع للميزة الجديدة
git checkout -b feature/amazing-feature

# أو إنشاء فرع لإصلاح خطأ
git checkout -b fix/bug-description
```

### 4. تطوير التغييرات
- اكتب كود نظيف ومفهوم
- أضف تعليقات باللغة العربية أو الإنجليزية
- اتبع معايير الكود المحددة
- اكتب اختبارات للكود الجديد

### 5. اختبار التغييرات
```bash
# تشغيل الاختبارات
npm test

# تشغيل linting
npm run lint

# تشغيل formatting
npm run format
```

### 6. Commit التغييرات
```bash
# إضافة الملفات المعدلة
git add .

# كتابة commit message واضح
git commit -m "feat: إضافة ميزة البحث المتقدم"
```

### 7. Push ورفع Pull Request
```bash
# رفع التغييرات
git push origin feature/amazing-feature

# ثم افتح Pull Request في GitHub
```

## 🐛 الإبلاغ عن الأخطاء

عند العثور على خطأ، يرجى:

### 1. التحقق من Issues الموجودة
ابحث في [Issues](https://github.com/your-username/arabic-ecommerce-platform/issues) للتأكد من عدم الإبلاغ عن الخطأ مسبقاً.

### 2. إنشاء Issue جديد
استخدم قالب Bug Report وقدم:

- **وصف واضح للخطأ**
- **خطوات إعادة الإنتاج**
- **السلوك المتوقع**
- **السلوك الفعلي**
- **لقطات شاشة (إن أمكن)**
- **معلومات البيئة:**
  - نظام التشغيل
  - إصدار Node.js
  - إصدار المتصفح
  - إصدار المشروع

### مثال على تقرير خطأ:
```markdown
## وصف الخطأ
لا يتم حفظ المنتج عند الضغط على زر "حفظ" في لوحة التحكم.

## خطوات إعادة الإنتاج
1. اذهب إلى لوحة التحكم
2. انقر على "إضافة منتج"
3. املأ جميع الحقول المطلوبة
4. انقر على "حفظ"

## السلوك المتوقع
يجب حفظ المنتج وإظهار رسالة نجاح.

## السلوك الفعلي
لا يحدث شيء، ولا تظهر أي رسالة خطأ.

## البيئة
- OS: Windows 10
- Node.js: v18.17.0
- Browser: Chrome 120.0.0.0
- Project Version: 1.0.0
```

## 💡 اقتراح مميزات جديدة

### 1. مناقشة الفكرة
قبل البدء في التطوير، افتح [Discussion](https://github.com/your-username/arabic-ecommerce-platform/discussions) لمناقشة الفكرة.

### 2. إنشاء Feature Request
استخدم قالب Feature Request وقدم:

- **وصف المشكلة التي تحلها الميزة**
- **الحل المقترح**
- **بدائل أخرى تم النظر فيها**
- **معلومات إضافية**

## 🛠️ إرشادات التطوير

### هيكل المشروع
```
arabic-ecommerce-platform/
├── backend/              # كود الخادم
│   ├── controllers/      # منطق التحكم
│   ├── models/          # نماذج البيانات
│   ├── routes/          # مسارات API
│   ├── middleware/      # الوسطاء
│   └── utils/           # أدوات مساعدة
├── frontend/            # كود العميل
│   ├── pages/           # صفحات HTML
│   ├── css/             # ملفات التنسيق
│   └── js/              # ملفات JavaScript
├── public/              # الملفات العامة
├── scripts/             # سكريبتات مساعدة
└── tests/               # الاختبارات
```

### تقنيات مستخدمة
- **Backend:** Node.js, Express.js, MongoDB, Mongoose
- **Frontend:** HTML5, CSS3, Bootstrap 5, JavaScript
- **Authentication:** JWT, bcrypt
- **File Upload:** Multer, Sharp
- **Testing:** Jest, Supertest
- **Documentation:** Swagger/OpenAPI

### متطلبات التطوير
- Node.js 16+ 
- MongoDB 4.4+
- Git
- محرر نصوص (VS Code موصى به)

## 📏 معايير الكود

### JavaScript
- استخدم ES6+ features
- اتبع معايير ESLint المحددة
- استخدم async/await بدلاً من callbacks
- اكتب تعليقات واضحة

```javascript
// ✅ جيد
const getUserById = async (id) => {
  try {
    const user = await User.findById(id);
    return user;
  } catch (error) {
    throw new Error(`خطأ في جلب المستخدم: ${error.message}`);
  }
};

// ❌ سيء
function getUserById(id, callback) {
  User.findById(id, function(err, user) {
    if (err) callback(err);
    callback(null, user);
  });
}
```

### CSS
- استخدم BEM methodology للتسمية
- اكتب CSS متجاوب
- استخدم CSS custom properties للألوان

```css
/* ✅ جيد */
.product-card {
  background: var(--white-color);
  border-radius: var(--border-radius);
}

.product-card__title {
  font-weight: 600;
  color: var(--dark-color);
}

/* ❌ سيء */
.productCard {
  background: #ffffff;
  border-radius: 8px;
}
```

### HTML
- استخدم HTML5 semantic elements
- اكتب markup accessible
- استخدم Arabic lang attributes

```html
<!-- ✅ جيد -->
<article class="product-card" lang="ar">
  <header class="product-card__header">
    <h2 class="product-card__title">اسم المنتج</h2>
  </header>
  <main class="product-card__content">
    <p class="product-card__description">وصف المنتج</p>
  </main>
</article>

<!-- ❌ سيء -->
<div class="product">
  <div class="title">اسم المنتج</div>
  <div class="desc">وصف المنتج</div>
</div>
```

### Commit Messages
استخدم [Conventional Commits](https://www.conventionalcommits.org/):

```bash
# أنواع الـ commits
feat: ميزة جديدة
fix: إصلاح خطأ
docs: تحديث التوثيق
style: تغييرات التنسيق
refactor: إعادة هيكلة الكود
test: إضافة اختبارات
chore: مهام صيانة

# أمثلة
git commit -m "feat: إضافة نظام البحث المتقدم"
git commit -m "fix: إصلاح خطأ في تسجيل الدخول"
git commit -m "docs: تحديث دليل التثبيت"
```

## 🔍 عملية المراجعة

### معايير قبول Pull Request
- [ ] الكود يتبع معايير المشروع
- [ ] جميع الاختبارات تمر بنجاح
- [ ] التوثيق محدث (إن لزم الأمر)
- [ ] لا توجد conflicts مع main branch
- [ ] الوصف واضح ومفصل

### عملية المراجعة
1. **Automated Checks:** تشغيل CI/CD تلقائياً
2. **Code Review:** مراجعة من قبل maintainers
3. **Testing:** اختبار يدوي إضافي
4. **Approval:** موافقة من maintainer واحد على الأقل
5. **Merge:** دمج في main branch

### نصائح للحصول على مراجعة سريعة
- اكتب وصف واضح للـ PR
- اجعل التغييرات صغيرة ومركزة
- أضف screenshots للتغييرات البصرية
- اربط الـ PR بـ Issue ذات صلة
- استجب للتعليقات بسرعة

## 👥 مجتمع المطورين

### قنوات التواصل
- **GitHub Discussions:** للمناقشات العامة
- **GitHub Issues:** للأخطاء والمميزات
- **Discord:** للدردشة المباشرة
- **Email:** للاستفسارات الخاصة

### قواعد السلوك
- كن محترماً ومهذباً
- ساعد الآخرين
- تقبل النقد البناء
- شارك المعرفة
- احترم التنوع

### الحصول على المساعدة
إذا كنت بحاجة لمساعدة:

1. ابحث في التوثيق أولاً
2. تحقق من Issues المغلقة
3. اسأل في Discussions
4. انضم لـ Discord للمساعدة السريعة

---

شكراً لك على اهتمامك بالمساهمة! 🙏

كل مساهمة، مهما كانت صغيرة، تساعد في تحسين المشروع وخدمة المجتمع العربي.
