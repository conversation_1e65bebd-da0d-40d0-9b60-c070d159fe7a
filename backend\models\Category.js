const mongoose = require('mongoose');
const slugify = require('slugify');

const categorySchema = new mongoose.Schema({
  name: {
    ar: {
      type: String,
      required: [true, 'اسم الفئة باللغة العربية مطلوب'],
      trim: true,
      maxlength: [50, 'اسم الفئة يجب أن يكون أقل من 50 حرف']
    },
    en: {
      type: String,
      trim: true,
      maxlength: [50, 'Category name must be less than 50 characters']
    }
  },
  slug: {
    type: String,
    unique: true
  },
  description: {
    ar: {
      type: String,
      maxlength: [500, 'الوصف يجب أن يكون أقل من 500 حرف']
    },
    en: {
      type: String,
      maxlength: [500, 'Description must be less than 500 characters']
    }
  },
  parent: {
    type: mongoose.Schema.ObjectId,
    ref: 'Category',
    default: null
  },
  level: {
    type: Number,
    default: 0,
    min: [0, 'المستوى لا يمكن أن يكون سالباً']
  },
  image: {
    url: String,
    alt: {
      ar: String,
      en: String
    }
  },
  icon: {
    type: String,
    trim: true
  },
  color: {
    type: String,
    match: [/^#([A-Fa-f0-9]{6}|[A-Fa-f0-9]{3})$/, 'يرجى إدخال لون صحيح بصيغة hex']
  },
  status: {
    type: String,
    enum: ['active', 'inactive'],
    default: 'active'
  },
  featured: {
    type: Boolean,
    default: false
  },
  sortOrder: {
    type: Number,
    default: 0
  },
  seo: {
    metaTitle: {
      ar: String,
      en: String
    },
    metaDescription: {
      ar: String,
      en: String
    },
    keywords: [String]
  },
  settings: {
    showInMenu: {
      type: Boolean,
      default: true
    },
    showInHomepage: {
      type: Boolean,
      default: false
    },
    productsPerPage: {
      type: Number,
      default: 20,
      min: [1, 'عدد المنتجات في الصفحة يجب أن يكون على الأقل 1']
    }
  },
  stats: {
    productCount: {
      type: Number,
      default: 0
    },
    totalSales: {
      type: Number,
      default: 0
    }
  },
  createdBy: {
    type: mongoose.Schema.ObjectId,
    ref: 'User'
  },
  updatedBy: {
    type: mongoose.Schema.ObjectId,
    ref: 'User'
  }
}, {
  timestamps: true,
  toJSON: { virtuals: true },
  toObject: { virtuals: true }
});

// Virtual for children categories
categorySchema.virtual('children', {
  ref: 'Category',
  localField: '_id',
  foreignField: 'parent',
  justOne: false
});

// Virtual for products in this category
categorySchema.virtual('products', {
  ref: 'Product',
  localField: '_id',
  foreignField: 'category',
  justOne: false
});

// Virtual for full path
categorySchema.virtual('path').get(function() {
  // This will be populated by a method
  return this._path || [];
});

// Indexes for better performance
categorySchema.index({ slug: 1 });
categorySchema.index({ parent: 1 });
categorySchema.index({ level: 1 });
categorySchema.index({ status: 1 });
categorySchema.index({ featured: 1 });
categorySchema.index({ sortOrder: 1 });
categorySchema.index({ 'name.ar': 'text', 'name.en': 'text', 'description.ar': 'text', 'description.en': 'text' });

// Generate slug before saving
categorySchema.pre('save', function(next) {
  if (this.isModified('name.ar') || this.isModified('name.en') || !this.slug) {
    const nameForSlug = this.name.en || this.name.ar;
    this.slug = slugify(nameForSlug, {
      lower: true,
      strict: true,
      locale: 'ar'
    });
  }
  next();
});

// Set level based on parent
categorySchema.pre('save', async function(next) {
  if (this.isModified('parent')) {
    if (this.parent) {
      const parentCategory = await this.constructor.findById(this.parent);
      if (parentCategory) {
        this.level = parentCategory.level + 1;
      }
    } else {
      this.level = 0;
    }
  }
  next();
});

// Prevent circular references
categorySchema.pre('save', async function(next) {
  if (this.parent && this.parent.toString() === this._id.toString()) {
    return next(new Error('الفئة لا يمكن أن تكون والد لنفسها'));
  }
  
  if (this.parent) {
    // Check if setting this parent would create a circular reference
    const isCircular = await this.constructor.checkCircularReference(this._id, this.parent);
    if (isCircular) {
      return next(new Error('لا يمكن إنشاء مرجع دائري في الفئات'));
    }
  }
  
  next();
});

// Update product count after save
categorySchema.post('save', async function() {
  await this.updateProductCount();
});

// Static method to check circular reference
categorySchema.statics.checkCircularReference = async function(categoryId, parentId) {
  let currentParent = parentId;
  const visited = new Set();
  
  while (currentParent) {
    if (visited.has(currentParent.toString())) {
      return true; // Circular reference detected
    }
    
    if (currentParent.toString() === categoryId.toString()) {
      return true; // Would create circular reference
    }
    
    visited.add(currentParent.toString());
    
    const parent = await this.findById(currentParent);
    currentParent = parent ? parent.parent : null;
  }
  
  return false;
};

// Static method to get category tree
categorySchema.statics.getTree = async function(parentId = null, maxLevel = null) {
  const query = { parent: parentId, status: 'active' };
  if (maxLevel !== null) {
    query.level = { $lte: maxLevel };
  }
  
  const categories = await this.find(query)
    .sort({ sortOrder: 1, 'name.ar': 1 })
    .lean();
  
  for (let category of categories) {
    category.children = await this.getTree(category._id, maxLevel);
  }
  
  return categories;
};

// Static method to get all descendants
categorySchema.statics.getDescendants = async function(categoryId) {
  const descendants = [];
  const children = await this.find({ parent: categoryId });
  
  for (let child of children) {
    descendants.push(child._id);
    const childDescendants = await this.getDescendants(child._id);
    descendants.push(...childDescendants);
  }
  
  return descendants;
};

// Static method to get category path
categorySchema.statics.getPath = async function(categoryId) {
  const path = [];
  let currentCategory = await this.findById(categoryId);
  
  while (currentCategory) {
    path.unshift({
      _id: currentCategory._id,
      name: currentCategory.name,
      slug: currentCategory.slug
    });
    
    if (currentCategory.parent) {
      currentCategory = await this.findById(currentCategory.parent);
    } else {
      currentCategory = null;
    }
  }
  
  return path;
};

// Method to update product count
categorySchema.methods.updateProductCount = async function() {
  const Product = mongoose.model('Product');
  
  // Get all descendants
  const descendants = await this.constructor.getDescendants(this._id);
  const categoryIds = [this._id, ...descendants];
  
  const count = await Product.countDocuments({
    category: { $in: categoryIds },
    status: 'active'
  });
  
  this.stats.productCount = count;
  await this.save();
  
  // Update parent categories recursively
  if (this.parent) {
    const parent = await this.constructor.findById(this.parent);
    if (parent) {
      await parent.updateProductCount();
    }
  }
};

// Method to get breadcrumb
categorySchema.methods.getBreadcrumb = async function() {
  return await this.constructor.getPath(this._id);
};

// Static method to get featured categories
categorySchema.statics.getFeatured = function(limit = 10) {
  return this.find({ featured: true, status: 'active' })
    .sort({ sortOrder: 1, 'name.ar': 1 })
    .limit(limit);
};

// Static method to search categories
categorySchema.statics.search = function(query, options = {}) {
  const searchQuery = {
    $text: { $search: query },
    status: 'active'
  };
  
  return this.find(searchQuery, { score: { $meta: 'textScore' } })
    .sort({ score: { $meta: 'textScore' } })
    .limit(options.limit || 20);
};

module.exports = mongoose.model('Category', categorySchema);
