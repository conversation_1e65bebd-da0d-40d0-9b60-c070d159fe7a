@echo off
chcp 65001 >nul
title Arabic E-commerce Platform - Quick Start

echo.
echo ╔══════════════════════════════════════════════════════════════╗
echo ║                                                              ║
echo ║           🛍️  Arabic E-commerce Platform 🛍️                  ║
echo ║                                                              ║
echo ║                    Quick Start Script                        ║
echo ║                                                              ║
echo ╚══════════════════════════════════════════════════════════════╝
echo.

echo 🔍 Checking prerequisites...

:: Check if Node.js is installed
node --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ Node.js is not installed!
    echo.
    echo 📥 Please install Node.js first:
    echo    1. Go to https://nodejs.org/
    echo    2. Download and install the LTS version
    echo    3. Restart this script
    echo.
    pause
    exit /b 1
)

echo ✅ Node.js is installed
node --version

:: Check if npm is available
npm --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ npm is not available!
    echo Please reinstall Node.js
    pause
    exit /b 1
)

echo ✅ npm is available
npm --version

:: Check if MongoDB is running (optional)
echo.
echo 🗄️  Checking MongoDB...
mongod --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ⚠️  MongoDB is not installed or not in PATH
    echo    You can either:
    echo    1. Install MongoDB Community Server locally
    echo    2. Use MongoDB Atlas (cloud database)
    echo    3. Update MONGODB_URI in .env file
) else (
    echo ✅ MongoDB is available
)

echo.
echo 📦 Installing dependencies...
call npm install
if %errorlevel% neq 0 (
    echo ❌ Failed to install dependencies
    pause
    exit /b 1
)

echo ✅ Dependencies installed successfully!

:: Check if .env exists
if not exist ".env" (
    echo.
    echo ⚙️  Creating .env file...
    if exist ".env.example" (
        copy ".env.example" ".env" >nul
        echo ✅ .env file created from .env.example
    ) else (
        echo ❌ .env.example not found
        echo Please create .env file manually
        pause
        exit /b 1
    )
) else (
    echo ✅ .env file exists
)

echo.
echo 🌱 Do you want to seed the database with sample data? (y/n)
set /p seed_choice=
if /i "%seed_choice%"=="y" (
    echo.
    echo 🌱 Seeding database...
    call npm run seed
    if %errorlevel% neq 0 (
        echo ⚠️  Seeding failed - you can run 'npm run seed' later
    ) else (
        echo ✅ Database seeded successfully!
    )
)

echo.
echo 🚀 Starting the server...
echo.
echo 🌐 The application will be available at:
echo    Main Site: http://localhost:3000
echo    Admin Panel: http://localhost:3000/admin
echo    API Docs: http://localhost:3000/api-docs
echo.
echo 👤 Test Accounts:
echo    Admin: <EMAIL> / admin123456
echo    User: <EMAIL> / user123456
echo.
echo Press Ctrl+C to stop the server
echo.

:: Start the server
call npm start

pause
