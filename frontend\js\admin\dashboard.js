// Admin Dashboard JavaScript

document.addEventListener('DOMContentLoaded', async () => {
  console.log('📊 Dashboard loaded');

  // Load dashboard data
  await loadDashboardStats();
  await loadRecentOrders();
  
  // Initialize charts
  initSalesChart();
  initCategoriesChart();
});

// Load dashboard statistics
async function loadDashboardStats() {
  try {
    // For now, we'll use mock data since the API endpoints are placeholders
    const mockStats = {
      totalOrders: 1247,
      totalRevenue: 125750,
      totalUsers: 892,
      totalProducts: 156
    };

    // Update statistics cards
    updateStatsCard('totalOrders', mockStats.totalOrders);
    updateStatsCard('totalRevenue', AdminUtils.formatCurrency(mockStats.totalRevenue));
    updateStatsCard('totalUsers', AdminUtils.formatNumber(mockStats.totalUsers));
    updateStatsCard('totalProducts', AdminUtils.formatNumber(mockStats.totalProducts));

    AdminState.stats = mockStats;

  } catch (error) {
    console.error('Failed to load dashboard stats:', error);
    
    // Show error state
    ['totalOrders', 'totalRevenue', 'totalUsers', 'totalProducts'].forEach(id => {
      updateStatsCard(id, 'خطأ');
    });
  }
}

// Update statistics card
function updateStatsCard(elementId, value) {
  const element = document.getElementById(elementId);
  if (element) {
    element.textContent = value;
    
    // Add animation
    element.style.opacity = '0';
    setTimeout(() => {
      element.style.transition = 'opacity 0.5s ease';
      element.style.opacity = '1';
    }, 100);
  }
}

// Load recent orders
async function loadRecentOrders() {
  try {
    // Mock data for recent orders
    const mockOrders = [
      {
        id: 'ORD-001',
        orderNumber: '#12345',
        customer: 'أحمد محمد',
        total: 450,
        status: 'pending',
        createdAt: new Date(Date.now() - 2 * 60 * 60 * 1000) // 2 hours ago
      },
      {
        id: 'ORD-002',
        orderNumber: '#12346',
        customer: 'فاطمة علي',
        total: 320,
        status: 'confirmed',
        createdAt: new Date(Date.now() - 4 * 60 * 60 * 1000) // 4 hours ago
      },
      {
        id: 'ORD-003',
        orderNumber: '#12347',
        customer: 'محمد أحمد',
        total: 180,
        status: 'processing',
        createdAt: new Date(Date.now() - 6 * 60 * 60 * 1000) // 6 hours ago
      },
      {
        id: 'ORD-004',
        orderNumber: '#12348',
        customer: 'سارة محمود',
        total: 275,
        status: 'shipped',
        createdAt: new Date(Date.now() - 8 * 60 * 60 * 1000) // 8 hours ago
      },
      {
        id: 'ORD-005',
        orderNumber: '#12349',
        customer: 'عبدالله سالم',
        total: 520,
        status: 'delivered',
        createdAt: new Date(Date.now() - 12 * 60 * 60 * 1000) // 12 hours ago
      }
    ];

    const tableBody = document.getElementById('recentOrdersTable');
    if (!tableBody) return;

    if (mockOrders.length === 0) {
      tableBody.innerHTML = `
        <tr>
          <td colspan="6" class="text-center text-muted py-4">
            <i class="fas fa-inbox fa-2x mb-2"></i>
            <br>لا توجد طلبات حديثة
          </td>
        </tr>
      `;
      return;
    }

    tableBody.innerHTML = mockOrders.map(order => `
      <tr>
        <td>
          <a href="/admin/orders/${order.id}" class="text-decoration-none fw-bold">
            ${order.orderNumber}
          </a>
        </td>
        <td>${order.customer}</td>
        <td class="fw-bold text-primary">${AdminUtils.formatCurrency(order.total)}</td>
        <td>${AdminUtils.getStatusBadge(order.status, 'order')}</td>
        <td class="text-muted small">${AdminUtils.formatDate(order.createdAt)}</td>
        <td>
          <div class="btn-group btn-group-sm">
            <a href="/admin/orders/${order.id}" class="btn btn-outline-primary btn-sm">
              <i class="fas fa-eye"></i>
            </a>
            <button class="btn btn-outline-secondary btn-sm" onclick="editOrder('${order.id}')">
              <i class="fas fa-edit"></i>
            </button>
          </div>
        </td>
      </tr>
    `).join('');

  } catch (error) {
    console.error('Failed to load recent orders:', error);
    
    const tableBody = document.getElementById('recentOrdersTable');
    if (tableBody) {
      tableBody.innerHTML = `
        <tr>
          <td colspan="6" class="text-center text-danger py-4">
            <i class="fas fa-exclamation-triangle fa-2x mb-2"></i>
            <br>حدث خطأ في تحميل الطلبات
          </td>
        </tr>
      `;
    }
  }
}

// Initialize sales chart
function initSalesChart() {
  const ctx = document.getElementById('salesChart');
  if (!ctx) return;

  // Mock sales data for the last 12 months
  const salesData = {
    labels: [
      'يناير', 'فبراير', 'مارس', 'أبريل', 'مايو', 'يونيو',
      'يوليو', 'أغسطس', 'سبتمبر', 'أكتوبر', 'نوفمبر', 'ديسمبر'
    ],
    datasets: [{
      label: 'المبيعات (ريال)',
      data: [12000, 15000, 18000, 14000, 22000, 25000, 28000, 24000, 26000, 30000, 32000, 35000],
      borderColor: AdminConfig.CHART_COLORS.primary,
      backgroundColor: AdminConfig.CHART_COLORS.primary + '20',
      borderWidth: 3,
      fill: true,
      tension: 0.4
    }]
  };

  AdminState.charts.sales = new Chart(ctx, {
    type: 'line',
    data: salesData,
    options: {
      responsive: true,
      maintainAspectRatio: false,
      plugins: {
        legend: {
          display: false
        },
        tooltip: {
          backgroundColor: 'rgba(0, 0, 0, 0.8)',
          titleColor: 'white',
          bodyColor: 'white',
          borderColor: AdminConfig.CHART_COLORS.primary,
          borderWidth: 1,
          callbacks: {
            label: function(context) {
              return AdminUtils.formatCurrency(context.parsed.y);
            }
          }
        }
      },
      scales: {
        y: {
          beginAtZero: true,
          ticks: {
            callback: function(value) {
              return AdminUtils.formatCurrency(value);
            }
          },
          grid: {
            color: 'rgba(0, 0, 0, 0.1)'
          }
        },
        x: {
          grid: {
            display: false
          }
        }
      },
      elements: {
        point: {
          radius: 6,
          hoverRadius: 8,
          backgroundColor: 'white',
          borderWidth: 3
        }
      }
    }
  });
}

// Initialize categories chart
function initCategoriesChart() {
  const ctx = document.getElementById('categoriesChart');
  if (!ctx) return;

  // Mock categories data
  const categoriesData = {
    labels: ['الإلكترونيات', 'الأزياء', 'المنزل والحديقة', 'الكتب', 'الرياضة'],
    datasets: [{
      data: [35, 25, 20, 12, 8],
      backgroundColor: [
        AdminConfig.CHART_COLORS.primary,
        AdminConfig.CHART_COLORS.success,
        AdminConfig.CHART_COLORS.warning,
        AdminConfig.CHART_COLORS.info,
        AdminConfig.CHART_COLORS.secondary
      ],
      borderWidth: 0,
      hoverBorderWidth: 3,
      hoverBorderColor: 'white'
    }]
  };

  AdminState.charts.categories = new Chart(ctx, {
    type: 'doughnut',
    data: categoriesData,
    options: {
      responsive: true,
      maintainAspectRatio: false,
      plugins: {
        legend: {
          position: 'bottom',
          labels: {
            padding: 20,
            usePointStyle: true,
            font: {
              family: 'Cairo',
              size: 12
            }
          }
        },
        tooltip: {
          backgroundColor: 'rgba(0, 0, 0, 0.8)',
          titleColor: 'white',
          bodyColor: 'white',
          borderColor: 'white',
          borderWidth: 1,
          callbacks: {
            label: function(context) {
              const percentage = ((context.parsed / context.dataset.data.reduce((a, b) => a + b, 0)) * 100).toFixed(1);
              return `${context.label}: ${percentage}%`;
            }
          }
        }
      },
      cutout: '60%',
      elements: {
        arc: {
          borderWidth: 0
        }
      }
    }
  });
}

// Edit order function
window.editOrder = function(orderId) {
  AdminUtils.showToast('سيتم توجيهك لصفحة تعديل الطلب', 'info');
  setTimeout(() => {
    window.location.href = `/admin/orders/${orderId}/edit`;
  }, 1000);
};

// Refresh dashboard data
async function refreshDashboard() {
  AdminUtils.showLoading();
  
  try {
    await loadDashboardStats();
    await loadRecentOrders();
    
    // Update charts with new data
    if (AdminState.charts.sales) {
      AdminState.charts.sales.update();
    }
    if (AdminState.charts.categories) {
      AdminState.charts.categories.update();
    }
    
    AdminUtils.showToast('تم تحديث البيانات بنجاح', 'success');
  } catch (error) {
    AdminUtils.showToast('حدث خطأ في تحديث البيانات', 'danger');
  } finally {
    AdminUtils.hideLoading();
  }
}

// Auto-refresh dashboard every 5 minutes
setInterval(refreshDashboard, 5 * 60 * 1000);

// Export functions for external use
window.refreshDashboard = refreshDashboard;
