# دليل الإعداد والتشغيل | Setup Guide

## 🚀 دليل التشغيل السريع

### المتطلبات الأساسية

قبل تشغيل المشروع، تأكد من تثبيت:

#### 1. Node.js (الإصدار 16 أو أحدث)
**تحميل وتثبيت Node.js:**
- اذهب إلى [nodejs.org](https://nodejs.org/)
- حمل النسخة LTS (Long Term Support)
- قم بتثبيتها مع الإعدادات الافتراضية
- أعد تشغيل Command Prompt أو PowerShell

**للتحقق من التثبيت:**
```bash
node --version
npm --version
```

#### 2. MongoDB (قاعدة البيانات)
**الخيار الأول - MongoDB Community Server (محلي):**
- اذهب إلى [mongodb.com/try/download/community](https://www.mongodb.com/try/download/community)
- حمل MongoDB Community Server
- قم بتثبيته مع الإعدادات الافتراضية
- تأكد من تشغيل MongoDB كخدمة

**الخيار الثاني - MongoDB Atlas (سحابي - مجاني):**
- اذهب إلى [mongodb.com/atlas](https://www.mongodb.com/atlas)
- أنشئ حساب مجاني
- أنشئ cluster جديد
- احصل على connection string

#### 3. Git (اختياري)
- اذهب إلى [git-scm.com](https://git-scm.com/)
- حمل وثبت Git for Windows

### خطوات التشغيل

#### الخطوة 1: تثبيت التبعيات
```bash
npm install
```

#### الخطوة 2: إعداد ملف البيئة
ملف `.env` موجود بالفعل، لكن تأكد من تحديث هذه القيم:

```env
# إذا كنت تستخدم MongoDB محلي
MONGODB_URI=mongodb://localhost:27017/arabic_ecommerce

# إذا كنت تستخدم MongoDB Atlas
MONGODB_URI=mongodb+srv://username:<EMAIL>/arabic_ecommerce

# إعدادات البريد الإلكتروني (اختياري للتطوير)
EMAIL_HOST=smtp.gmail.com
EMAIL_PORT=587
EMAIL_USER=<EMAIL>
EMAIL_PASS=your_app_password
```

#### الخطوة 3: إضافة البيانات التجريبية
```bash
npm run seed
```

#### الخطوة 4: تشغيل المشروع
```bash
# للتطوير (مع إعادة التشغيل التلقائي)
npm run dev

# أو للتشغيل العادي
npm start
```

### الوصول للموقع

بعد التشغيل الناجح:
- **الموقع الرئيسي:** http://localhost:3000
- **لوحة التحكم:** http://localhost:3000/admin
- **توثيق API:** http://localhost:3000/api-docs

### الحسابات التجريبية

**حساب المدير:**
- البريد الإلكتروني: `<EMAIL>`
- كلمة المرور: `admin123456`

**حساب مستخدم عادي:**
- البريد الإلكتروني: `<EMAIL>`
- كلمة المرور: `user123456`

## 🛠️ استكشاف الأخطاء

### خطأ: "npm is not recognized"
**السبب:** Node.js غير مثبت أو غير موجود في PATH
**الحل:**
1. ثبت Node.js من [nodejs.org](https://nodejs.org/)
2. أعد تشغيل Command Prompt
3. تحقق من التثبيت: `node --version`

### خطأ: "connect ECONNREFUSED 127.0.0.1:27017"
**السبب:** MongoDB غير مشغل
**الحل:**
1. **Windows:** اذهب إلى Services وابحث عن MongoDB، وتأكد أنه مشغل
2. **أو:** شغل MongoDB يدوياً: `mongod`
3. **أو:** استخدم MongoDB Atlas (سحابي)

### خطأ: "Port 3000 is already in use"
**السبب:** المنفذ 3000 مستخدم من تطبيق آخر
**الحل:**
1. غير المنفذ في ملف `.env`: `PORT=3001`
2. أو أوقف التطبيق الآخر

### خطأ في تثبيت التبعيات
**الحل:**
```bash
# امسح node_modules وأعد التثبيت
rm -rf node_modules package-lock.json
npm install

# أو استخدم yarn
npm install -g yarn
yarn install
```

## 📱 أوامر مفيدة

```bash
# تشغيل الاختبارات
npm test

# فحص الكود
npm run lint

# تنسيق الكود
npm run format

# إعادة تعيين قاعدة البيانات
npm run seed:fresh

# تشغيل مع clustering
npm run app

# إعداد سريع تفاعلي
npm run setup
```

## 🌐 إعداد الإنتاج

### متغيرات البيئة للإنتاج
```env
NODE_ENV=production
MONGODB_URI=your_production_mongodb_uri
JWT_SECRET=your_very_secure_jwt_secret_here
EMAIL_HOST=your_smtp_host
EMAIL_USER=your_email
EMAIL_PASS=your_email_password
```

### تشغيل في الإنتاج
```bash
# تثبيت PM2 لإدارة العمليات
npm install -g pm2

# تشغيل التطبيق
pm2 start server.js --name "arabic-ecommerce"

# مراقبة التطبيق
pm2 monit

# إعادة تشغيل
pm2 restart arabic-ecommerce
```

## 📞 الحصول على المساعدة

إذا واجهت أي مشاكل:

1. **تحقق من هذا الدليل أولاً**
2. **راجع ملف README.md**
3. **ابحث في Issues على GitHub**
4. **افتح Issue جديد مع تفاصيل المشكلة**

### معلومات مفيدة عند طلب المساعدة:
- نظام التشغيل
- إصدار Node.js (`node --version`)
- إصدار npm (`npm --version`)
- رسالة الخطأ الكاملة
- خطوات إعادة الإنتاج

---

**نصيحة:** احفظ هذا الدليل كمرجع سريع! 📖
