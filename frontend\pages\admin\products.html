<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إدارة المنتجات - لوحة التحكم</title>
    <meta name="description" content="إدارة المنتجات في المتجر الإلكتروني">
    
    <!-- Bootstrap 5 RTL -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.rtl.min.css" rel="stylesheet">
    
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    
    <!-- Google Fonts - Arabic -->
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">
    
    <!-- Custom CSS -->
    <link href="/css/style.css" rel="stylesheet">
    <link href="/css/admin.css" rel="stylesheet">
    
    <!-- Favicon -->
    <link rel="icon" type="image/x-icon" href="/images/favicon.ico">
</head>
<body class="admin-body">
    <!-- Admin Navigation -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-dark sticky-top">
        <div class="container-fluid">
            <!-- Logo -->
            <a class="navbar-brand fw-bold" href="/admin">
                <i class="fas fa-cogs me-2"></i>
                لوحة التحكم
            </a>
            
            <!-- Mobile Toggle -->
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#adminNavbar">
                <span class="navbar-toggler-icon"></span>
            </button>
            
            <!-- Navigation Links -->
            <div class="collapse navbar-collapse" id="adminNavbar">
                <ul class="navbar-nav me-auto">
                    <li class="nav-item">
                        <a class="nav-link" href="/admin">
                            <i class="fas fa-tachometer-alt me-2"></i>الرئيسية
                        </a>
                    </li>
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle active" href="#" role="button" data-bs-toggle="dropdown">
                            <i class="fas fa-box me-2"></i>المنتجات
                        </a>
                        <ul class="dropdown-menu">
                            <li><a class="dropdown-item active" href="/admin/products">جميع المنتجات</a></li>
                            <li><a class="dropdown-item" href="/admin/products/add">إضافة منتج</a></li>
                            <li><a class="dropdown-item" href="/admin/categories">الفئات</a></li>
                        </ul>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="/admin/orders">
                            <i class="fas fa-shopping-cart me-2"></i>الطلبات
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="/admin/users">
                            <i class="fas fa-users me-2"></i>المستخدمون
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="/admin/analytics">
                            <i class="fas fa-chart-bar me-2"></i>التحليلات
                        </a>
                    </li>
                </ul>
                
                <!-- Admin User Menu -->
                <div class="d-flex align-items-center">
                    <div class="dropdown">
                        <button class="btn btn-outline-light dropdown-toggle" type="button" data-bs-toggle="dropdown">
                            <i class="fas fa-user-shield me-2"></i>
                            <span id="adminUserName">المدير</span>
                        </button>
                        <ul class="dropdown-menu dropdown-menu-end">
                            <li><a class="dropdown-item" href="/admin/profile">الملف الشخصي</a></li>
                            <li><a class="dropdown-item" href="/admin/settings">الإعدادات</a></li>
                            <li><hr class="dropdown-divider"></li>
                            <li><a class="dropdown-item" href="/" target="_blank">عرض المتجر</a></li>
                            <li><hr class="dropdown-divider"></li>
                            <li><a class="dropdown-item" href="#" onclick="logout()">تسجيل الخروج</a></li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    </nav>

    <!-- Main Content -->
    <div class="container-fluid py-4">
        <!-- Page Header -->
        <div class="row mb-4">
            <div class="col-12">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <h1 class="h3 mb-1">إدارة المنتجات</h1>
                        <p class="text-muted mb-0">إدارة وتنظيم منتجات المتجر</p>
                    </div>
                    <div>
                        <a href="/admin/products/add" class="btn btn-primary">
                            <i class="fas fa-plus me-2"></i>إضافة منتج جديد
                        </a>
                    </div>
                </div>
            </div>
        </div>

        <!-- Filters and Search -->
        <div class="row mb-4">
            <div class="col-12">
                <div class="card border-0 shadow-sm">
                    <div class="card-body">
                        <div class="row g-3">
                            <!-- Search -->
                            <div class="col-md-4">
                                <label class="form-label">البحث</label>
                                <div class="input-group">
                                    <input type="text" class="form-control" id="searchInput" placeholder="ابحث في المنتجات...">
                                    <button class="btn btn-outline-secondary" type="button" id="searchBtn">
                                        <i class="fas fa-search"></i>
                                    </button>
                                </div>
                            </div>
                            
                            <!-- Category Filter -->
                            <div class="col-md-3">
                                <label class="form-label">الفئة</label>
                                <select class="form-select" id="categoryFilter">
                                    <option value="">جميع الفئات</option>
                                    <!-- Categories will be loaded dynamically -->
                                </select>
                            </div>
                            
                            <!-- Status Filter -->
                            <div class="col-md-2">
                                <label class="form-label">الحالة</label>
                                <select class="form-select" id="statusFilter">
                                    <option value="">جميع الحالات</option>
                                    <option value="active">نشط</option>
                                    <option value="inactive">غير نشط</option>
                                    <option value="draft">مسودة</option>
                                    <option value="archived">مؤرشف</option>
                                </select>
                            </div>
                            
                            <!-- Sort -->
                            <div class="col-md-2">
                                <label class="form-label">الترتيب</label>
                                <select class="form-select" id="sortBy">
                                    <option value="-createdAt">الأحدث</option>
                                    <option value="name.ar">الاسم (أ-ي)</option>
                                    <option value="-name.ar">الاسم (ي-أ)</option>
                                    <option value="price">السعر (الأقل)</option>
                                    <option value="-price">السعر (الأعلى)</option>
                                    <option value="-rating.average">الأعلى تقييماً</option>
                                </select>
                            </div>
                            
                            <!-- Clear Filters -->
                            <div class="col-md-1 d-flex align-items-end">
                                <button class="btn btn-outline-secondary w-100" id="clearFilters">
                                    <i class="fas fa-times"></i>
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Products Table -->
        <div class="row">
            <div class="col-12">
                <div class="card border-0 shadow-sm">
                    <div class="card-header bg-white d-flex justify-content-between align-items-center">
                        <h5 class="card-title mb-0">
                            <i class="fas fa-box me-2"></i>قائمة المنتجات
                            <span class="badge bg-primary ms-2" id="productsCount">0</span>
                        </h5>
                        <div class="d-flex gap-2">
                            <!-- Bulk Actions -->
                            <div class="dropdown">
                                <button class="btn btn-outline-secondary dropdown-toggle" type="button" data-bs-toggle="dropdown" disabled id="bulkActionsBtn">
                                    إجراءات متعددة
                                </button>
                                <ul class="dropdown-menu">
                                    <li><a class="dropdown-item" href="#" onclick="bulkAction('activate')">تفعيل المحدد</a></li>
                                    <li><a class="dropdown-item" href="#" onclick="bulkAction('deactivate')">إلغاء تفعيل المحدد</a></li>
                                    <li><hr class="dropdown-divider"></li>
                                    <li><a class="dropdown-item text-danger" href="#" onclick="bulkAction('delete')">حذف المحدد</a></li>
                                </ul>
                            </div>
                            
                            <!-- Export -->
                            <button class="btn btn-outline-success" onclick="exportProducts()">
                                <i class="fas fa-download me-2"></i>تصدير
                            </button>
                        </div>
                    </div>
                    <div class="card-body p-0">
                        <div class="table-responsive">
                            <table class="table table-hover mb-0">
                                <thead class="table-light">
                                    <tr>
                                        <th width="50">
                                            <div class="form-check">
                                                <input class="form-check-input" type="checkbox" id="selectAll">
                                            </div>
                                        </th>
                                        <th width="80">الصورة</th>
                                        <th>اسم المنتج</th>
                                        <th>الفئة</th>
                                        <th>السعر</th>
                                        <th>المخزون</th>
                                        <th>الحالة</th>
                                        <th>التاريخ</th>
                                        <th width="120">الإجراءات</th>
                                    </tr>
                                </thead>
                                <tbody id="productsTableBody">
                                    <!-- Products will be loaded dynamically -->
                                </tbody>
                            </table>
                        </div>
                    </div>
                    
                    <!-- Pagination -->
                    <div class="card-footer bg-white">
                        <div class="d-flex justify-content-between align-items-center">
                            <div class="text-muted">
                                عرض <span id="showingFrom">0</span> إلى <span id="showingTo">0</span> من <span id="totalProducts">0</span> منتج
                            </div>
                            <nav>
                                <ul class="pagination mb-0" id="pagination">
                                    <!-- Pagination will be generated dynamically -->
                                </ul>
                            </nav>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Product Modal -->
    <div class="modal fade" id="productModal" tabindex="-1">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">تفاصيل المنتج</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body" id="productModalBody">
                    <!-- Product details will be loaded here -->
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إغلاق</button>
                    <a href="#" class="btn btn-primary" id="editProductBtn">تعديل المنتج</a>
                </div>
            </div>
        </div>
    </div>

    <!-- Loading Spinner -->
    <div id="loadingSpinner" class="d-none">
        <div class="spinner-border text-primary" role="status">
            <span class="visually-hidden">جاري التحميل...</span>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js"></script>
    
    <!-- Custom JS -->
    <script src="/js/app.js"></script>
    <script src="/js/admin/admin.js"></script>
    <script src="/js/admin/products.js"></script>
</body>
</html>
