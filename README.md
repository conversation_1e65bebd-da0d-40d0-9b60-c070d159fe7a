# متجر إلكتروني عربي احترافي | Arabic E-commerce Platform

موقع إلكتروني احترافي وحديث لبيع المنتجات والخدمات مع لوحة تحكم إدارية متكاملة، مصمم خصيصاً للسوق العربي مع دعم كامل للغة العربية (RTL).

## المميزات الرئيسية

### 🛍️ واجهة المستخدم
- تصميم عصري ومتجاوب مع جميع الأجهزة
- دعم كامل للغة العربية (RTL) والإنجليزية
- صفحة رئيسية جذابة مع عروض وأقسام مميزة
- نظام بحث متقدم مع فلترة ذكية
- سلة تسوق تفاعلية وسهلة الاستخدام
- نظام تقييم ومراجعات للمنتجات
- صفحات منتجات تفصيلية مع صور متعددة

### 🔐 نظام المصادقة والأمان
- تسجيل دخول آمن مع JWT
- تشفير كلمات المرور باستخدام bcrypt
- تأكيد البريد الإلكتروني
- استعادة كلمة المرور
- حماية من CSRF وXSS
- Rate limiting للحماية من الهجمات

### 📦 إدارة المنتجات
- إضافة وتعديل المنتجات مع صور متعددة
- نظام فئات هرمي متقدم
- إدارة المخزون والمتغيرات
- نظام SKU وباركود
- خصائص ومواصفات مفصلة
- نظام علامات وكلمات مفتاحية

### 🛒 إدارة الطلبات
- نظام طلبات متكامل
- تتبع حالة الطلب
- إدارة الشحن والتوصيل
- فواتير إلكترونية
- نظام إرجاع واسترداد

### 👥 لوحة التحكم الإدارية
- لوحة معلومات شاملة مع إحصائيات
- إدارة المستخدمين والصلاحيات
- إدارة المنتجات والفئات
- إدارة الطلبات والمبيعات
- تقارير مفصلة وتحليلات
- نظام إشعارات متقدم

### 💳 نظام الدفع والخصومات
- دعم طرق دفع متعددة
- نظام كوبونات وخصومات
- حساب الضرائب تلقائياً
- فواتير مفصلة

## التقنيات المستخدمة

### Backend
- **Node.js** - بيئة تشغيل JavaScript
- **Express.js** - إطار عمل الخادم
- **MongoDB** - قاعدة البيانات
- **Mongoose** - ODM لـ MongoDB
- **JWT** - المصادقة والتوكن
- **bcryptjs** - تشفير كلمات المرور
- **Multer & Sharp** - رفع ومعالجة الصور
- **Nodemailer** - إرسال البريد الإلكتروني

### Frontend
- **HTML5** - هيكل الصفحات
- **CSS3** - التنسيق والتصميم
- **Bootstrap 5** - إطار عمل CSS
- **JavaScript** - التفاعل والديناميكية
- **Font Awesome** - الأيقونات

### الأمان والحماية
- **Helmet** - حماية HTTP headers
- **CORS** - إدارة الطلبات المتقاطعة
- **Express Rate Limit** - حماية من الهجمات
- **Express Validator** - التحقق من البيانات

## متطلبات التشغيل

- Node.js (الإصدار 16 أو أحدث)
- MongoDB (الإصدار 4.4 أو أحدث)
- npm أو yarn

## المتطلبات الأساسية

قبل البدء، تأكد من تثبيت:

### 🟢 Node.js (الإصدار 16 أو أحدث)
- **Windows/Mac:** حمل من [nodejs.org](https://nodejs.org/)
- **Ubuntu/Debian:** `sudo apt install nodejs npm`
- **CentOS/RHEL:** `sudo yum install nodejs npm`

### 🟢 MongoDB (قاعدة البيانات)
**الخيار الأول - محلي:**
- **Windows:** [MongoDB Community Server](https://www.mongodb.com/try/download/community)
- **Mac:** `brew install mongodb-community`
- **Ubuntu:** `sudo apt install mongodb`

**الخيار الثاني - سحابي (مجاني):**
- [MongoDB Atlas](https://www.mongodb.com/atlas) - 512MB مجاني

### 🔧 أدوات إضافية (اختياري)
- **Git:** لاستنساخ المشروع
- **VS Code:** محرر نصوص موصى به
- **Postman:** لاختبار APIs

## التثبيت والإعداد

### الطريقة السريعة (موصى بها)

#### للمستخدمين الجدد - تشغيل مباشر:
```bash
# Windows - Command Prompt
start.bat

# Windows - PowerShell
.\start.ps1

# Linux/Mac - Terminal
npm run setup
```

#### للمطورين المتقدمين:
```bash
# 1. استنساخ المشروع
git clone https://github.com/your-username/arabic-ecommerce-platform.git
cd arabic-ecommerce-platform

# 2. تشغيل الإعداد السريع
npm run setup
```

سيقوم الإعداد السريع بـ:
- فحص المتطلبات الأساسية (Node.js, MongoDB)
- إنشاء ملف .env تلقائياً
- تثبيت التبعيات
- إضافة بيانات تجريبية (اختياري)
- تشغيل الخادم (اختياري)

### الطريقة اليدوية

#### 1. استنساخ المشروع
```bash
git clone https://github.com/your-username/arabic-ecommerce-platform.git
cd arabic-ecommerce-platform
```

#### 2. تثبيت التبعيات
```bash
npm install
```

#### 3. إعداد متغيرات البيئة
```bash
cp .env.example .env
```

قم بتعديل ملف `.env` وإضافة القيم المطلوبة:

```env
NODE_ENV=development
PORT=3000
MONGODB_URI=mongodb://localhost:27017/arabic_ecommerce
JWT_SECRET=your_super_secret_jwt_key_here_change_in_production
EMAIL_HOST=smtp.gmail.com
EMAIL_PORT=587
EMAIL_USER=<EMAIL>
EMAIL_PASS=your_app_password
EMAIL_FROM=<EMAIL>
ADMIN_EMAIL=<EMAIL>
ADMIN_PASSWORD=admin123456
STORE_NAME=متجرنا الإلكتروني
```

#### 4. تشغيل قاعدة البيانات
تأكد من تشغيل MongoDB على جهازك أو استخدم MongoDB Atlas.

#### 5. إضافة بيانات تجريبية (اختياري)
```bash
npm run seed
```

#### 6. تشغيل المشروع
```bash
# للتطوير مع إعادة التشغيل التلقائي
npm run dev

# للإنتاج
npm start

# تشغيل مع clustering
npm run app
```

## الوصول للموقع

- **الموقع الرئيسي**: http://localhost:3000
- **لوحة التحكم**: http://localhost:3000/admin
- **توثيق API**: http://localhost:3000/api-docs (في بيئة التطوير)

## حسابات تجريبية

### حساب المدير
- البريد الإلكتروني: <EMAIL>
- كلمة المرور: admin123456

### حساب مستخدم عادي
- البريد الإلكتروني: <EMAIL>
- كلمة المرور: user123456

## هيكل المشروع

```
arabic-ecommerce-platform/
├── backend/
│   ├── config/          # إعدادات قاعدة البيانات
│   ├── controllers/     # منطق التحكم
│   ├── middleware/      # الوسطاء
│   ├── models/          # نماذج البيانات
│   ├── routes/          # مسارات API
│   └── utils/           # أدوات مساعدة
├── frontend/
│   ├── css/             # ملفات التنسيق
│   ├── js/              # ملفات JavaScript
│   ├── images/          # الصور
│   └── pages/           # صفحات HTML
├── public/
│   ├── css/             # ملفات CSS المجمعة
│   ├── js/              # ملفات JS المجمعة
│   ├── images/          # الصور العامة
│   └── uploads/         # الملفات المرفوعة
├── scripts/             # سكريبتات مساعدة
├── docs/                # التوثيق
└── tests/               # الاختبارات
```

## API Documentation

يمكنك الوصول لتوثيق API الكامل على:
http://localhost:3000/api-docs

### أهم نقاط API

#### المصادقة
- `POST /api/auth/register` - تسجيل مستخدم جديد
- `POST /api/auth/login` - تسجيل الدخول
- `GET /api/auth/me` - الحصول على بيانات المستخدم الحالي

#### المنتجات
- `GET /api/products` - قائمة المنتجات
- `GET /api/products/:id` - تفاصيل منتج
- `POST /api/products` - إضافة منتج (مدير)
- `PUT /api/products/:id` - تحديث منتج (مدير)

#### الفئات
- `GET /api/categories` - قائمة الفئات
- `GET /api/categories/tree` - شجرة الفئات
- `POST /api/categories` - إضافة فئة (مدير)

## الاختبار

```bash
# تشغيل جميع الاختبارات
npm test

# تشغيل اختبارات محددة
npm test -- --grep "Auth"
```

## النشر

### النشر على Heroku
1. إنشاء تطبيق Heroku جديد
2. إضافة MongoDB Atlas كقاعدة بيانات
3. تعيين متغيرات البيئة
4. رفع الكود

```bash
heroku create your-app-name
heroku config:set NODE_ENV=production
heroku config:set MONGODB_URI=your_mongodb_atlas_uri
git push heroku main
```

### النشر على DigitalOcean
1. إنشاء Droplet جديد
2. تثبيت Node.js و MongoDB
3. استنساخ المشروع
4. إعداد PM2 للتشغيل المستمر

## المساهمة

نرحب بمساهماتكم! يرجى قراءة دليل المساهمة قبل إرسال Pull Request.

1. Fork المشروع
2. إنشاء فرع للميزة الجديدة (`git checkout -b feature/AmazingFeature`)
3. Commit التغييرات (`git commit -m 'Add some AmazingFeature'`)
4. Push للفرع (`git push origin feature/AmazingFeature`)
5. فتح Pull Request

## الترخيص

هذا المشروع مرخص تحت رخصة MIT - راجع ملف [LICENSE](LICENSE) للتفاصيل.

## الدعم

إذا واجهت أي مشاكل أو لديك أسئلة:

- افتح Issue جديد في GitHub
- راسلنا على: <EMAIL>
- انضم لمجتمعنا على Discord

## استكشاف الأخطاء وإصلاحها

### مشاكل شائعة وحلولها

#### خطأ في الاتصال بقاعدة البيانات
```
Error: connect ECONNREFUSED 127.0.0.1:27017
```
**الحل:**
- تأكد من تشغيل MongoDB على جهازك
- تحقق من صحة MONGODB_URI في ملف .env
- للتثبيت على Windows: [MongoDB Community Server](https://www.mongodb.com/try/download/community)
- للتثبيت على macOS: `brew install mongodb-community`
- للتثبيت على Ubuntu: `sudo apt install mongodb`

#### خطأ في إرسال البريد الإلكتروني
```
Error: Invalid login: 535-5.7.8 Username and Password not accepted
```
**الحل:**
- تأكد من صحة بيانات البريد الإلكتروني في ملف .env
- لـ Gmail: استخدم App Password بدلاً من كلمة المرور العادية
- فعّل "Less secure app access" أو استخدم OAuth2

#### خطأ في رفع الملفات
```
Error: ENOENT: no such file or directory, open 'public/uploads/...'
```
**الحل:**
- تأكد من وجود مجلد public/uploads
- تشغيل: `mkdir -p public/uploads/{products,categories,avatars,general}`
- تحقق من صلاحيات الكتابة على المجلد

#### خطأ في JWT Token
```
Error: jwt malformed
```
**الحل:**
- تأكد من وجود JWT_SECRET في ملف .env
- امسح cookies المتصفح وأعد تسجيل الدخول
- تأكد من عدم انتهاء صلاحية التوكن

#### مشكلة في تحميل الصفحات
```
Cannot GET /admin
```
**الحل:**
- تأكد من تشغيل الخادم على المنفذ الصحيح
- تحقق من إعدادات CORS في ملف .env
- امسح cache المتصفح

### أوامر مفيدة للتشخيص

```bash
# فحص حالة قاعدة البيانات
npm run test:db

# فحص الاتصال بالبريد الإلكتروني
npm run test:email

# عرض logs مفصلة
DEBUG=* npm run dev

# إعادة تعيين قاعدة البيانات
npm run seed:fresh

# فحص المنافذ المستخدمة
netstat -tulpn | grep :3000
```

### طلب المساعدة

إذا واجهت مشكلة لم تُذكر هنا:

1. تحقق من [Issues](https://github.com/your-username/arabic-ecommerce-platform/issues) الموجودة
2. ابحث في [الوثائق](https://github.com/your-username/arabic-ecommerce-platform/wiki)
3. افتح Issue جديد مع تفاصيل المشكلة
4. انضم لمجتمعنا على [Discord](https://discord.gg/your-server)

## الشكر والتقدير

شكر خاص لجميع المساهمين والمكتبات مفتوحة المصدر المستخدمة في هذا المشروع:

- [Node.js](https://nodejs.org/) - بيئة تشغيل JavaScript
- [Express.js](https://expressjs.com/) - إطار عمل الخادم
- [MongoDB](https://www.mongodb.com/) - قاعدة البيانات
- [Bootstrap](https://getbootstrap.com/) - إطار عمل CSS
- [Font Awesome](https://fontawesome.com/) - الأيقونات
- [Chart.js](https://www.chartjs.org/) - الرسوم البيانية

---

صُنع بـ ❤️ للمجتمع العربي

**المطورون:**
- [اسمك هنا](https://github.com/your-username)

**الترخيص:** MIT License

**الإصدار:** 1.0.0
