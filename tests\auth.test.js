const request = require('supertest');
const app = require('../server');
const User = require('../backend/models/User');

describe('Authentication Endpoints', () => {
  describe('POST /api/auth/register', () => {
    it('should register a new user successfully', async () => {
      const userData = {
        name: 'أحمد محمد',
        email: '<EMAIL>',
        password: 'Password123',
        phone: '+966501234567'
      };

      const response = await request(app)
        .post('/api/auth/register')
        .send(userData)
        .expect(201);

      expect(response.body.success).toBe(true);
      expect(response.body.token).toBeDefined();
      expect(response.body.user.email).toBe(userData.email);
      expect(response.body.user.password).toBeUndefined();
    });

    it('should not register user with invalid email', async () => {
      const userData = {
        name: 'Test User',
        email: 'invalid-email',
        password: 'Password123',
        phone: '+966501234567'
      };

      const response = await request(app)
        .post('/api/auth/register')
        .send(userData)
        .expect(400);

      expect(response.body.success).toBe(false);
      expect(response.body.message).toContain('بريد إلكتروني صحيح');
    });

    it('should not register user with weak password', async () => {
      const userData = {
        name: 'Test User',
        email: '<EMAIL>',
        password: '123',
        phone: '+966501234567'
      };

      const response = await request(app)
        .post('/api/auth/register')
        .send(userData)
        .expect(400);

      expect(response.body.success).toBe(false);
      expect(response.body.message).toContain('كلمة المرور');
    });

    it('should not register user with duplicate email', async () => {
      const userData = {
        name: 'Test User',
        email: '<EMAIL>',
        password: 'Password123',
        phone: '+966501234567'
      };

      // Register first user
      await request(app)
        .post('/api/auth/register')
        .send(userData)
        .expect(201);

      // Try to register with same email
      const response = await request(app)
        .post('/api/auth/register')
        .send({ ...userData, name: 'Another User' })
        .expect(400);

      expect(response.body.success).toBe(false);
      expect(response.body.message).toContain('مستخدم');
    });
  });

  describe('POST /api/auth/login', () => {
    beforeEach(async () => {
      // Create a test user
      await global.testHelpers.createTestUser({
        email: '<EMAIL>',
        password: 'Password123'
      });
    });

    it('should login user with correct credentials', async () => {
      const loginData = {
        email: '<EMAIL>',
        password: 'Password123'
      };

      const response = await request(app)
        .post('/api/auth/login')
        .send(loginData)
        .expect(200);

      expect(response.body.success).toBe(true);
      expect(response.body.token).toBeDefined();
      expect(response.body.user.email).toBe(loginData.email);
    });

    it('should not login with incorrect password', async () => {
      const loginData = {
        email: '<EMAIL>',
        password: 'WrongPassword'
      };

      const response = await request(app)
        .post('/api/auth/login')
        .send(loginData)
        .expect(401);

      expect(response.body.success).toBe(false);
      expect(response.body.message).toContain('غير صحيحة');
    });

    it('should not login with non-existent email', async () => {
      const loginData = {
        email: '<EMAIL>',
        password: 'Password123'
      };

      const response = await request(app)
        .post('/api/auth/login')
        .send(loginData)
        .expect(401);

      expect(response.body.success).toBe(false);
      expect(response.body.message).toContain('غير صحيحة');
    });

    it('should not login inactive user', async () => {
      // Create inactive user
      await global.testHelpers.createTestUser({
        email: '<EMAIL>',
        password: 'Password123',
        isActive: false
      });

      const loginData = {
        email: '<EMAIL>',
        password: 'Password123'
      };

      const response = await request(app)
        .post('/api/auth/login')
        .send(loginData)
        .expect(401);

      expect(response.body.success).toBe(false);
      expect(response.body.message).toContain('إيقاف');
    });
  });

  describe('GET /api/auth/me', () => {
    let user, token;

    beforeEach(async () => {
      user = await global.testHelpers.createTestUser();
      token = global.testHelpers.generateTestToken(user);
    });

    it('should get current user data with valid token', async () => {
      const response = await request(app)
        .get('/api/auth/me')
        .set('Authorization', `Bearer ${token}`)
        .expect(200);

      expect(response.body.success).toBe(true);
      expect(response.body.data._id).toBe(user._id.toString());
      expect(response.body.data.email).toBe(user.email);
    });

    it('should not get user data without token', async () => {
      const response = await request(app)
        .get('/api/auth/me')
        .expect(401);

      expect(response.body.success).toBe(false);
      expect(response.body.message).toContain('مصرح');
    });

    it('should not get user data with invalid token', async () => {
      const response = await request(app)
        .get('/api/auth/me')
        .set('Authorization', 'Bearer invalid-token')
        .expect(401);

      expect(response.body.success).toBe(false);
      expect(response.body.message).toContain('غير صحيح');
    });
  });

  describe('PUT /api/auth/updatedetails', () => {
    let user, token;

    beforeEach(async () => {
      user = await global.testHelpers.createTestUser();
      token = global.testHelpers.generateTestToken(user);
    });

    it('should update user details successfully', async () => {
      const updateData = {
        name: 'اسم محدث',
        phone: '+966509876543'
      };

      const response = await request(app)
        .put('/api/auth/updatedetails')
        .set('Authorization', `Bearer ${token}`)
        .send(updateData)
        .expect(200);

      expect(response.body.success).toBe(true);
      expect(response.body.data.name).toBe(updateData.name);
      expect(response.body.data.phone).toBe(updateData.phone);
    });

    it('should not update with invalid phone number', async () => {
      const updateData = {
        phone: 'invalid-phone'
      };

      const response = await request(app)
        .put('/api/auth/updatedetails')
        .set('Authorization', `Bearer ${token}`)
        .send(updateData)
        .expect(400);

      expect(response.body.success).toBe(false);
      expect(response.body.message).toContain('هاتف');
    });
  });

  describe('PUT /api/auth/updatepassword', () => {
    let user, token;

    beforeEach(async () => {
      user = await global.testHelpers.createTestUser({
        password: 'OldPassword123'
      });
      token = global.testHelpers.generateTestToken(user);
    });

    it('should update password successfully', async () => {
      const passwordData = {
        currentPassword: 'OldPassword123',
        newPassword: 'NewPassword123'
      };

      const response = await request(app)
        .put('/api/auth/updatepassword')
        .set('Authorization', `Bearer ${token}`)
        .send(passwordData)
        .expect(200);

      expect(response.body.success).toBe(true);
      expect(response.body.token).toBeDefined();
    });

    it('should not update password with wrong current password', async () => {
      const passwordData = {
        currentPassword: 'WrongPassword',
        newPassword: 'NewPassword123'
      };

      const response = await request(app)
        .put('/api/auth/updatepassword')
        .set('Authorization', `Bearer ${token}`)
        .send(passwordData)
        .expect(401);

      expect(response.body.success).toBe(false);
      expect(response.body.message).toContain('الحالية غير صحيحة');
    });
  });

  describe('GET /api/auth/logout', () => {
    it('should logout successfully', async () => {
      const response = await request(app)
        .get('/api/auth/logout')
        .expect(200);

      expect(response.body.success).toBe(true);
      expect(response.body.message).toContain('تسجيل الخروج');
    });
  });
});
