/* Arabic E-commerce Platform - Custom Styles */

/* Import Google Fonts */
@import url('https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap');

/* Root Variables */
:root {
  --primary-color: #007bff;
  --secondary-color: #6c757d;
  --success-color: #28a745;
  --danger-color: #dc3545;
  --warning-color: #ffc107;
  --info-color: #17a2b8;
  --light-color: #f8f9fa;
  --dark-color: #343a40;
  --white-color: #ffffff;
  --border-radius: 8px;
  --box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  --transition: all 0.3s ease;
}

/* Base Styles */
* {
  box-sizing: border-box;
}

body {
  font-family: 'Cairo', sans-serif;
  line-height: 1.6;
  color: var(--dark-color);
  background-color: var(--white-color);
  direction: rtl;
  text-align: right;
}

/* Typography */
h1, h2, h3, h4, h5, h6 {
  font-weight: 600;
  margin-bottom: 1rem;
}

.display-4 {
  font-weight: 700;
}

/* Navigation */
.navbar {
  padding: 1rem 0;
  transition: var(--transition);
}

.navbar-brand {
  font-size: 1.5rem;
  font-weight: 700;
}

.navbar-nav .nav-link {
  font-weight: 500;
  padding: 0.5rem 1rem;
  transition: var(--transition);
}

.navbar-nav .nav-link:hover {
  color: var(--primary-color) !important;
}

.navbar-nav .nav-link.active {
  color: var(--primary-color) !important;
  font-weight: 600;
}

/* Hero Section */
.hero-section {
  background: linear-gradient(135deg, var(--primary-color) 0%, #0056b3 100%);
  min-height: 500px;
  display: flex;
  align-items: center;
}

.hero-section img {
  max-height: 400px;
  object-fit: cover;
}

/* Features Section */
.features-section {
  background-color: var(--white-color);
}

.feature-card {
  background: var(--white-color);
  border-radius: var(--border-radius);
  box-shadow: var(--box-shadow);
  transition: var(--transition);
  height: 100%;
}

.feature-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 5px 20px rgba(0, 0, 0, 0.15);
}

/* Categories Section */
.categories-section {
  background-color: var(--light-color);
}

.category-card {
  background: var(--white-color);
  border-radius: var(--border-radius);
  overflow: hidden;
  box-shadow: var(--box-shadow);
  transition: var(--transition);
  text-decoration: none;
  color: inherit;
  height: 100%;
}

.category-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 5px 20px rgba(0, 0, 0, 0.15);
  text-decoration: none;
  color: inherit;
}

.category-card img {
  width: 100%;
  height: 200px;
  object-fit: cover;
}

.category-card .card-body {
  padding: 1.5rem;
  text-align: center;
}

/* Products Section */
.product-card {
  background: var(--white-color);
  border-radius: var(--border-radius);
  overflow: hidden;
  box-shadow: var(--box-shadow);
  transition: var(--transition);
  height: 100%;
  position: relative;
}

.product-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 5px 20px rgba(0, 0, 0, 0.15);
}

.product-card img {
  width: 100%;
  height: 250px;
  object-fit: cover;
}

.product-card .card-body {
  padding: 1.5rem;
}

.product-price {
  font-size: 1.25rem;
  font-weight: 700;
  color: var(--primary-color);
}

.product-price-old {
  font-size: 1rem;
  color: var(--secondary-color);
  text-decoration: line-through;
}

.product-discount {
  position: absolute;
  top: 10px;
  left: 10px;
  background: var(--danger-color);
  color: white;
  padding: 0.25rem 0.5rem;
  border-radius: var(--border-radius);
  font-size: 0.875rem;
  font-weight: 600;
}

.product-rating {
  color: var(--warning-color);
}

/* Buttons */
.btn {
  border-radius: var(--border-radius);
  font-weight: 500;
  padding: 0.75rem 1.5rem;
  transition: var(--transition);
  border: none;
}

.btn-primary {
  background: var(--primary-color);
  border-color: var(--primary-color);
}

.btn-primary:hover {
  background: #0056b3;
  border-color: #0056b3;
  transform: translateY(-2px);
}

.btn-outline-primary {
  color: var(--primary-color);
  border-color: var(--primary-color);
}

.btn-outline-primary:hover {
  background: var(--primary-color);
  border-color: var(--primary-color);
  transform: translateY(-2px);
}

.btn-lg {
  padding: 1rem 2rem;
  font-size: 1.125rem;
}

/* Forms */
.form-control {
  border-radius: var(--border-radius);
  border: 2px solid #e9ecef;
  padding: 0.75rem 1rem;
  transition: var(--transition);
}

.form-control:focus {
  border-color: var(--primary-color);
  box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
}

.input-group .form-control {
  border-left: none;
}

.input-group .btn {
  border-right: none;
}

/* Newsletter Section */
.newsletter-section {
  background: linear-gradient(135deg, var(--primary-color) 0%, #0056b3 100%);
}

/* Footer */
.footer {
  background-color: var(--dark-color);
}

.footer a {
  transition: var(--transition);
}

.footer a:hover {
  color: var(--primary-color) !important;
}

.social-links a {
  display: inline-block;
  width: 40px;
  height: 40px;
  line-height: 40px;
  text-align: center;
  border-radius: 50%;
  background: rgb(255, 255, 255);
  transition: var(--transition);
}

.social-links a:hover {
  background: var(--primary-color);
  transform: translateY(-2px);
}

/* Loading Spinner */
#loadingSpinner {
  position: fixed;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  z-index: 9999;
}

/* Utilities */
.text-primary {
  color: var(--primary-color) !important;
}

.bg-primary {
  background-color: var(--primary-color) !important;
}

.shadow-sm {
  box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075) !important;
}

.rounded {
  border-radius: var(--border-radius) !important;
}

/* Responsive Design */
@media (max-width: 768px) {
  .hero-section {
    text-align: center;
    padding: 3rem 0;
  }
  
  .hero-section .col-lg-6:first-child {
    margin-bottom: 2rem;
  }
  
  .display-4 {
    font-size: 2rem;
  }
  
  .btn-lg {
    padding: 0.75rem 1.5rem;
    font-size: 1rem;
  }
  
  .feature-card,
  .category-card,
  .product-card {
    margin-bottom: 1.5rem;
  }
}

@media (max-width: 576px) {
  .container {
    padding-left: 1rem;
    padding-right: 1rem;
  }
  
  .navbar-brand {
    font-size: 1.25rem;
  }
  
  .hero-section {
    padding: 2rem 0;
  }
  
  .display-4 {
    font-size: 1.75rem;
  }
  
  .lead {
    font-size: 1rem;
  }
}

/* Animation Classes */
.fade-in {
  opacity: 0;
  transform: translateY(20px);
  transition: opacity 0.6s ease, transform 0.6s ease;
}

.fade-in.visible {
  opacity: 1;
  transform: translateY(0);
}

.slide-in-left {
  opacity: 0;
  transform: translateX(-50px);
  transition: opacity 0.6s ease, transform 0.6s ease;
}

.slide-in-left.visible {
  opacity: 1;
  transform: translateX(0);
}

.slide-in-right {
  opacity: 0;
  transform: translateX(50px);
  transition: opacity 0.6s ease, transform 0.6s ease;
}

.slide-in-right.visible {
  opacity: 1;
  transform: translateX(0);
}

/* Custom Scrollbar */
::-webkit-scrollbar {
  width: 8px;
}

::-webkit-scrollbar-track {
  background: var(--light-color);
}

::-webkit-scrollbar-thumb {
  background: var(--secondary-color);
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: var(--primary-color);
}
