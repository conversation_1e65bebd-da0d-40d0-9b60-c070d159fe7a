const mongoose = require('mongoose');
const { MongoMemoryServer } = require('mongodb-memory-server');

// Global test variables
global.mongoServer = null;

// Setup before all tests
beforeAll(async () => {
  // Set test environment
  process.env.NODE_ENV = 'test';
  process.env.JWT_SECRET = 'test_jwt_secret_key_for_testing_only';
  process.env.BCRYPT_ROUNDS = '4'; // Faster for testing
  
  // Start in-memory MongoDB instance
  global.mongoServer = await MongoMemoryServer.create();
  const mongoUri = global.mongoServer.getUri();
  
  // Connect to the in-memory database
  await mongoose.connect(mongoUri, {
    useNewUrlParser: true,
    useUnifiedTopology: true,
  });
  
  console.log('🧪 Test database connected');
});

// Cleanup after each test
afterEach(async () => {
  // Clear all collections
  const collections = mongoose.connection.collections;
  
  for (const key in collections) {
    const collection = collections[key];
    await collection.deleteMany({});
  }
});

// Cleanup after all tests
afterAll(async () => {
  // Close database connection
  await mongoose.connection.dropDatabase();
  await mongoose.connection.close();
  
  // Stop the in-memory MongoDB instance
  if (global.mongoServer) {
    await global.mongoServer.stop();
  }
  
  console.log('🧪 Test database disconnected');
});

// Global test helpers
global.testHelpers = {
  // Create test user
  createTestUser: async (userData = {}) => {
    const User = require('../backend/models/User');
    const defaultUser = {
      name: 'Test User',
      email: '<EMAIL>',
      password: 'password123',
      phone: '+966501234567',
      isEmailVerified: true
    };
    
    const user = new User({ ...defaultUser, ...userData });
    await user.save();
    return user;
  },
  
  // Create test admin
  createTestAdmin: async (userData = {}) => {
    return global.testHelpers.createTestUser({
      name: 'Test Admin',
      email: '<EMAIL>',
      role: 'admin',
      ...userData
    });
  },
  
  // Create test category
  createTestCategory: async (categoryData = {}) => {
    const Category = require('../backend/models/Category');
    const defaultCategory = {
      name: { ar: 'فئة تجريبية', en: 'Test Category' },
      status: 'active'
    };
    
    const category = new Category({ ...defaultCategory, ...categoryData });
    await category.save();
    return category;
  },
  
  // Create test product
  createTestProduct: async (productData = {}) => {
    const Product = require('../backend/models/Product');
    
    // Create category if not provided
    let category = productData.category;
    if (!category) {
      category = await global.testHelpers.createTestCategory();
      productData.category = category._id;
    }
    
    const defaultProduct = {
      name: { ar: 'منتج تجريبي', en: 'Test Product' },
      description: { ar: 'وصف المنتج التجريبي', en: 'Test product description' },
      price: 100,
      stock: { quantity: 50 },
      status: 'active'
    };
    
    const product = new Product({ ...defaultProduct, ...productData });
    await product.save();
    return product;
  },
  
  // Generate JWT token for testing
  generateTestToken: (user) => {
    const jwt = require('jsonwebtoken');
    return jwt.sign(
      { id: user._id, role: user.role },
      process.env.JWT_SECRET,
      { expiresIn: '1h' }
    );
  },
  
  // Wait for a specified time (for async operations)
  wait: (ms) => new Promise(resolve => setTimeout(resolve, ms))
};

// Suppress console.log during tests (optional)
if (process.env.SUPPRESS_TEST_LOGS === 'true') {
  global.console = {
    ...console,
    log: jest.fn(),
    info: jest.fn(),
    warn: jest.fn(),
    error: console.error // Keep error logs for debugging
  };
}

// Handle unhandled promise rejections in tests
process.on('unhandledRejection', (reason, promise) => {
  console.error('Unhandled Rejection at:', promise, 'reason:', reason);
});

// Handle uncaught exceptions in tests
process.on('uncaughtException', (error) => {
  console.error('Uncaught Exception:', error);
});
