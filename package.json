{"name": "arabic-ecommerce-platform", "version": "1.0.0", "description": "موقع إلكتروني احترافي لبيع المنتجات والخدمات مع لوحة تحكم إدارية - Arabic E-commerce Platform", "main": "server.js", "scripts": {"start": "node server.js", "dev": "nodemon server.js", "app": "node app.js", "setup": "node scripts/quick-start.js", "seed": "node scripts/seed.js", "seed:fresh": "npm run seed && npm start", "test": "jest", "test:watch": "jest --watch", "build": "npm run build:css && npm run build:js", "build:css": "node-sass public/scss/main.scss public/css/style.css --output-style compressed", "build:js": "webpack --mode production", "watch:css": "node-sass public/scss/main.scss public/css/style.css --watch", "lint": "eslint .", "lint:fix": "eslint . --fix", "format": "prettier --write .", "clean": "rm -rf node_modules package-lock.json && npm install", "docs": "node server.js && echo 'API Documentation available at http://localhost:3000/api-docs'", "postinstall": "echo '✅ Installation complete! Run npm run setup for quick start'"}, "keywords": ["ecommerce", "arabic", "rtl", "nodejs", "express", "mongodb", "bootstrap", "jwt"], "author": "Arabic E-commerce Team", "license": "MIT", "dependencies": {"express": "^4.18.2", "mongoose": "^7.5.0", "bcryptjs": "^2.4.3", "jsonwebtoken": "^9.0.2", "cors": "^2.8.5", "helmet": "^7.0.0", "express-rate-limit": "^6.10.0", "express-validator": "^7.0.1", "multer": "^1.4.5-lts.1", "sharp": "^0.32.5", "nodemailer": "^6.9.4", "dotenv": "^16.3.1", "compression": "^1.7.4", "morgan": "^1.10.0", "swagger-jsdoc": "^6.2.8", "swagger-ui-express": "^5.0.0", "express-session": "^1.17.3", "connect-mongo": "^5.0.0", "cookie-parser": "^1.4.6", "express-fileupload": "^1.4.0", "moment": "^2.29.4", "slugify": "^1.6.6", "validator": "^13.11.0"}, "devDependencies": {"nodemon": "^3.0.1", "jest": "^29.6.4", "supertest": "^6.3.3", "mongodb-memory-server": "^8.15.1", "jest-watch-typeahead": "^2.2.2", "eslint": "^8.48.0", "prettier": "^3.0.3", "node-sass": "^9.0.0", "webpack": "^5.88.2", "webpack-cli": "^5.1.4"}, "engines": {"node": ">=16.0.0", "npm": ">=8.0.0"}}