const express = require('express');
const { protect, authorize } = require('../middleware/auth');
const { 
  uploadSingle, 
  uploadMultiple,
  validateFileUpload,
  deleteImage,
  generateThumbnail
} = require('../middleware/upload');
const { AppError, asyncHandler } = require('../middleware/errorHandler');

const router = express.Router();

/**
 * @swagger
 * components:
 *   schemas:
 *     UploadResponse:
 *       type: object
 *       properties:
 *         success:
 *           type: boolean
 *         message:
 *           type: string
 *         data:
 *           type: object
 *           properties:
 *             url:
 *               type: string
 *               description: رابط الملف المرفوع
 *             originalName:
 *               type: string
 *               description: الاسم الأصلي للملف
 *             size:
 *               type: number
 *               description: حجم الملف بالبايت
 *             mimetype:
 *               type: string
 *               description: نوع الملف
 */

/**
 * @swagger
 * /api/upload/image:
 *   post:
 *     summary: رفع صورة واحدة
 *     tags: [Upload]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         multipart/form-data:
 *           schema:
 *             type: object
 *             required:
 *               - image
 *             properties:
 *               image:
 *                 type: string
 *                 format: binary
 *                 description: الصورة المراد رفعها
 *               directory:
 *                 type: string
 *                 description: المجلد المراد الحفظ فيه
 *                 enum: [general, products, categories, avatars, reviews]
 *                 default: general
 *               width:
 *                 type: integer
 *                 description: عرض الصورة المطلوب
 *                 default: 800
 *               height:
 *                 type: integer
 *                 description: ارتفاع الصورة المطلوب
 *                 default: 600
 *               quality:
 *                 type: integer
 *                 description: جودة الصورة (1-100)
 *                 minimum: 1
 *                 maximum: 100
 *                 default: 80
 *     responses:
 *       200:
 *         description: تم رفع الصورة بنجاح
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/UploadResponse'
 *       400:
 *         description: خطأ في الملف المرفوع
 *       401:
 *         description: غير مصرح
 */
router.post('/image', protect, (req, res, next) => {
  const options = {
    directory: req.body.directory || 'general',
    width: parseInt(req.body.width) || 800,
    height: parseInt(req.body.height) || 600,
    quality: parseInt(req.body.quality) || 80,
    prefix: 'img-'
  };

  const uploadMiddleware = uploadSingle('image', options);
  uploadMiddleware(req, res, next);
}, validateFileUpload, asyncHandler(async (req, res, next) => {
  if (!req.uploadedImage) {
    return next(new AppError(
      'لم يتم رفع أي صورة',
      400,
      'No image uploaded'
    ));
  }

  res.status(200).json({
    success: true,
    message: 'تم رفع الصورة بنجاح',
    messageEn: 'Image uploaded successfully',
    data: req.uploadedImage
  });
}));

/**
 * @swagger
 * /api/upload/images:
 *   post:
 *     summary: رفع عدة صور
 *     tags: [Upload]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         multipart/form-data:
 *           schema:
 *             type: object
 *             required:
 *               - images
 *             properties:
 *               images:
 *                 type: array
 *                 items:
 *                   type: string
 *                   format: binary
 *                 description: الصور المراد رفعها
 *               directory:
 *                 type: string
 *                 description: المجلد المراد الحفظ فيه
 *                 enum: [general, products, categories, avatars, reviews]
 *                 default: general
 *               width:
 *                 type: integer
 *                 description: عرض الصورة المطلوب
 *                 default: 800
 *               height:
 *                 type: integer
 *                 description: ارتفاع الصورة المطلوب
 *                 default: 600
 *               quality:
 *                 type: integer
 *                 description: جودة الصورة (1-100)
 *                 minimum: 1
 *                 maximum: 100
 *                 default: 80
 *     responses:
 *       200:
 *         description: تم رفع الصور بنجاح
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                 message:
 *                   type: string
 *                 count:
 *                   type: integer
 *                 data:
 *                   type: array
 *                   items:
 *                     type: object
 *                     properties:
 *                       url:
 *                         type: string
 *                       originalName:
 *                         type: string
 *                       size:
 *                         type: number
 *                       mimetype:
 *                         type: string
 *       400:
 *         description: خطأ في الملفات المرفوعة
 *       401:
 *         description: غير مصرح
 */
router.post('/images', protect, (req, res, next) => {
  const options = {
    directory: req.body.directory || 'general',
    width: parseInt(req.body.width) || 800,
    height: parseInt(req.body.height) || 600,
    quality: parseInt(req.body.quality) || 80,
    prefix: 'img-'
  };

  const maxCount = parseInt(req.body.maxCount) || 10;
  const uploadMiddleware = uploadMultiple('images', maxCount, options);
  uploadMiddleware(req, res, next);
}, asyncHandler(async (req, res, next) => {
  if (!req.uploadedImages || req.uploadedImages.length === 0) {
    return next(new AppError(
      'لم يتم رفع أي صور',
      400,
      'No images uploaded'
    ));
  }

  res.status(200).json({
    success: true,
    message: 'تم رفع الصور بنجاح',
    messageEn: 'Images uploaded successfully',
    count: req.uploadedImages.length,
    data: req.uploadedImages
  });
}));

/**
 * @swagger
 * /api/upload/avatar:
 *   post:
 *     summary: رفع صورة شخصية
 *     tags: [Upload]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         multipart/form-data:
 *           schema:
 *             type: object
 *             required:
 *               - avatar
 *             properties:
 *               avatar:
 *                 type: string
 *                 format: binary
 *                 description: الصورة الشخصية
 *     responses:
 *       200:
 *         description: تم رفع الصورة الشخصية بنجاح
 *       400:
 *         description: خطأ في الملف المرفوع
 *       401:
 *         description: غير مصرح
 */
router.post('/avatar', protect, (req, res, next) => {
  const uploadMiddleware = uploadSingle('avatar', {
    directory: 'avatars',
    width: 200,
    height: 200,
    quality: 80,
    prefix: `avatar-${req.user.id}-`,
    fit: 'cover'
  });
  uploadMiddleware(req, res, next);
}, validateFileUpload, asyncHandler(async (req, res, next) => {
  if (!req.uploadedImage) {
    return next(new AppError(
      'لم يتم رفع أي صورة',
      400,
      'No image uploaded'
    ));
  }

  // Update user avatar
  const User = require('../models/User');
  await User.findByIdAndUpdate(req.user.id, {
    avatar: req.uploadedImage.url
  });

  res.status(200).json({
    success: true,
    message: 'تم رفع الصورة الشخصية بنجاح',
    messageEn: 'Avatar uploaded successfully',
    data: req.uploadedImage
  });
}));

/**
 * @swagger
 * /api/upload/delete:
 *   delete:
 *     summary: حذف صورة
 *     tags: [Upload]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - imagePath
 *             properties:
 *               imagePath:
 *                 type: string
 *                 description: مسار الصورة المراد حذفها
 *                 example: /uploads/products/product-1234567890.webp
 *     responses:
 *       200:
 *         description: تم حذف الصورة بنجاح
 *       400:
 *         description: مسار الصورة مطلوب
 *       401:
 *         description: غير مصرح
 *       403:
 *         description: ليس لديك صلاحية
 */
router.delete('/delete', protect, authorize('admin', 'manager'), asyncHandler(async (req, res, next) => {
  const { imagePath } = req.body;

  if (!imagePath) {
    return next(new AppError(
      'مسار الصورة مطلوب',
      400,
      'Image path is required'
    ));
  }

  const success = await deleteImage(imagePath);

  if (!success) {
    return next(new AppError(
      'فشل في حذف الصورة',
      500,
      'Failed to delete image'
    ));
  }

  res.status(200).json({
    success: true,
    message: 'تم حذف الصورة بنجاح',
    messageEn: 'Image deleted successfully'
  });
}));

/**
 * @swagger
 * /api/upload/thumbnail:
 *   post:
 *     summary: إنشاء صورة مصغرة
 *     tags: [Upload]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - imagePath
 *             properties:
 *               imagePath:
 *                 type: string
 *                 description: مسار الصورة الأصلية
 *                 example: /uploads/products/product-1234567890.webp
 *               width:
 *                 type: integer
 *                 description: عرض الصورة المصغرة
 *                 default: 150
 *               height:
 *                 type: integer
 *                 description: ارتفاع الصورة المصغرة
 *                 default: 150
 *               quality:
 *                 type: integer
 *                 description: جودة الصورة المصغرة
 *                 default: 70
 *     responses:
 *       200:
 *         description: تم إنشاء الصورة المصغرة بنجاح
 *       400:
 *         description: مسار الصورة مطلوب
 *       401:
 *         description: غير مصرح
 */
router.post('/thumbnail', protect, authorize('admin', 'manager'), asyncHandler(async (req, res, next) => {
  const { imagePath, width, height, quality } = req.body;

  if (!imagePath) {
    return next(new AppError(
      'مسار الصورة مطلوب',
      400,
      'Image path is required'
    ));
  }

  const thumbnailPath = await generateThumbnail(imagePath, {
    width: width || 150,
    height: height || 150,
    quality: quality || 70
  });

  if (!thumbnailPath) {
    return next(new AppError(
      'فشل في إنشاء الصورة المصغرة',
      500,
      'Failed to generate thumbnail'
    ));
  }

  res.status(200).json({
    success: true,
    message: 'تم إنشاء الصورة المصغرة بنجاح',
    messageEn: 'Thumbnail generated successfully',
    data: {
      originalPath: imagePath,
      thumbnailPath
    }
  });
}));

module.exports = router;
