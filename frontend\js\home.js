// Home Page JavaScript

document.addEventListener('DOMContentLoaded', async () => {
  console.log('🏠 Home page loaded');

  // Load featured categories
  await loadFeaturedCategories();

  // Load featured products
  await loadFeaturedProducts();

  // Initialize newsletter form
  initNewsletterForm();

  // Add scroll animations
  initScrollAnimations();
});

// Load and display featured categories
async function loadFeaturedCategories() {
  try {
    const response = await API.categories.getFeatured(6);
    const categories = response.data;
    
    const container = document.getElementById('featuredCategories');
    if (!container) return;

    if (categories.length === 0) {
      container.innerHTML = `
        <div class="col-12 text-center">
          <p class="text-muted">لا توجد فئات متاحة حالياً</p>
        </div>
      `;
      return;
    }

    container.innerHTML = categories.map(category => `
      <div class="col-lg-4 col-md-6 mb-4">
        <a href="/products?category=${category._id}" class="category-card d-block text-decoration-none fade-in">
          <div class="card h-100 border-0 shadow-sm">
            <div class="position-relative">
              <img src="${category.image?.url || '/images/category-placeholder.jpg'}" 
                   class="card-img-top" 
                   alt="${category.name.ar}"
                   loading="lazy">
              <div class="position-absolute top-0 start-0 w-100 h-100 d-flex align-items-center justify-content-center bg-dark bg-opacity-25">
                <div class="text-center text-white">
                  ${category.icon ? `<i class="${category.icon} fa-3x mb-2"></i>` : ''}
                  <h5 class="fw-bold">${category.name.ar}</h5>
                  ${category.description?.ar ? `<p class="small mb-0">${category.description.ar}</p>` : ''}
                </div>
              </div>
            </div>
          </div>
        </a>
      </div>
    `).join('');

  } catch (error) {
    console.error('Failed to load featured categories:', error);
    const container = document.getElementById('featuredCategories');
    if (container) {
      container.innerHTML = `
        <div class="col-12 text-center">
          <div class="alert alert-warning">
            <i class="fas fa-exclamation-triangle me-2"></i>
            حدث خطأ في تحميل الفئات
          </div>
        </div>
      `;
    }
  }
}

// Load and display featured products
async function loadFeaturedProducts() {
  try {
    const response = await API.products.getFeatured(8);
    const products = response.data;
    
    const container = document.getElementById('featuredProducts');
    if (!container) return;

    if (products.length === 0) {
      container.innerHTML = `
        <div class="col-12 text-center">
          <p class="text-muted">لا توجد منتجات متاحة حالياً</p>
        </div>
      `;
      return;
    }

    container.innerHTML = products.map(product => createProductCard(product)).join('');

    // Add click handlers for add to cart buttons
    container.querySelectorAll('.add-to-cart-btn').forEach(btn => {
      btn.addEventListener('click', (e) => {
        e.preventDefault();
        const productId = btn.dataset.productId;
        const product = products.find(p => p._id === productId);
        if (product) {
          Cart.addItem(product, 1);
        }
      });
    });

  } catch (error) {
    console.error('Failed to load featured products:', error);
    const container = document.getElementById('featuredProducts');
    if (container) {
      container.innerHTML = `
        <div class="col-12 text-center">
          <div class="alert alert-warning">
            <i class="fas fa-exclamation-triangle me-2"></i>
            حدث خطأ في تحميل المنتجات
          </div>
        </div>
      `;
    }
  }
}

// Create product card HTML
function createProductCard(product) {
  const mainImage = product.images?.find(img => img.isMain) || product.images?.[0];
  const imageUrl = mainImage?.url || '/images/product-placeholder.jpg';
  const discountPercentage = product.discountPercentage || 0;
  const rating = product.rating?.average || 0;
  const reviewCount = product.rating?.count || 0;

  return `
    <div class="col-lg-3 col-md-6 mb-4">
      <div class="product-card fade-in">
        ${discountPercentage > 0 ? `<div class="product-discount">-${discountPercentage}%</div>` : ''}
        
        <a href="/product/${product._id}">
          <img src="${imageUrl}" 
               class="card-img-top" 
               alt="${product.name.ar}"
               loading="lazy">
        </a>
        
        <div class="card-body">
          <h6 class="card-title">
            <a href="/product/${product._id}" class="text-decoration-none text-dark">
              ${product.name.ar}
            </a>
          </h6>
          
          ${product.shortDescription?.ar ? `
            <p class="card-text text-muted small">${product.shortDescription.ar}</p>
          ` : ''}
          
          <div class="d-flex align-items-center mb-2">
            <div class="product-rating me-2">
              ${generateStarRating(rating)}
            </div>
            <small class="text-muted">(${reviewCount})</small>
          </div>
          
          <div class="d-flex justify-content-between align-items-center">
            <div>
              <span class="product-price">${Utils.formatPrice(product.price)}</span>
              ${product.comparePrice && product.comparePrice > product.price ? `
                <br><small class="product-price-old">${Utils.formatPrice(product.comparePrice)}</small>
              ` : ''}
            </div>
            
            <button class="btn btn-primary btn-sm add-to-cart-btn" 
                    data-product-id="${product._id}"
                    ${product.isOutOfStock ? 'disabled' : ''}>
              <i class="fas fa-cart-plus"></i>
              ${product.isOutOfStock ? 'نفد المخزون' : 'أضف للسلة'}
            </button>
          </div>
          
          ${product.isLowStock && !product.isOutOfStock ? `
            <div class="mt-2">
              <small class="text-warning">
                <i class="fas fa-exclamation-triangle"></i>
                كمية محدودة متبقية
              </small>
            </div>
          ` : ''}
        </div>
      </div>
    </div>
  `;
}

// Generate star rating HTML
function generateStarRating(rating) {
  const fullStars = Math.floor(rating);
  const hasHalfStar = rating % 1 >= 0.5;
  const emptyStars = 5 - fullStars - (hasHalfStar ? 1 : 0);
  
  let starsHtml = '';
  
  // Full stars
  for (let i = 0; i < fullStars; i++) {
    starsHtml += '<i class="fas fa-star"></i>';
  }
  
  // Half star
  if (hasHalfStar) {
    starsHtml += '<i class="fas fa-star-half-alt"></i>';
  }
  
  // Empty stars
  for (let i = 0; i < emptyStars; i++) {
    starsHtml += '<i class="far fa-star"></i>';
  }
  
  return starsHtml;
}

// Initialize newsletter form
function initNewsletterForm() {
  const form = document.getElementById('newsletterForm');
  if (!form) return;

  form.addEventListener('submit', async (e) => {
    e.preventDefault();
    
    const emailInput = form.querySelector('input[type="email"]');
    const email = emailInput.value.trim();
    
    if (!email) {
      Utils.showToast('يرجى إدخال بريد إلكتروني صحيح', 'warning');
      return;
    }

    try {
      // Simulate newsletter subscription
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      Utils.showToast('تم الاشتراك في النشرة الإخبارية بنجاح!', 'success');
      emailInput.value = '';
      
    } catch (error) {
      Utils.showToast('حدث خطأ في الاشتراك، يرجى المحاولة لاحقاً', 'danger');
    }
  });
}

// Initialize scroll animations
function initScrollAnimations() {
  // Add animation classes to elements
  const elementsToAnimate = [
    { selector: '.hero-section .col-lg-6:first-child', class: 'slide-in-right' },
    { selector: '.hero-section .col-lg-6:last-child', class: 'slide-in-left' },
    { selector: '.feature-card', class: 'fade-in' },
    { selector: '.category-card', class: 'fade-in' },
    { selector: '.product-card', class: 'fade-in' }
  ];

  elementsToAnimate.forEach(({ selector, class: animationClass }) => {
    document.querySelectorAll(selector).forEach((element, index) => {
      element.classList.add(animationClass);
      element.style.animationDelay = `${index * 0.1}s`;
    });
  });

  // Intersection Observer for animations
  const observerOptions = {
    threshold: 0.1,
    rootMargin: '0px 0px -50px 0px'
  };

  const observer = new IntersectionObserver((entries) => {
    entries.forEach(entry => {
      if (entry.isIntersecting) {
        entry.target.classList.add('visible');
        observer.unobserve(entry.target);
      }
    });
  }, observerOptions);

  // Observe all animated elements
  document.querySelectorAll('.fade-in, .slide-in-left, .slide-in-right').forEach(el => {
    observer.observe(el);
  });
}

// Add smooth scrolling for anchor links
document.querySelectorAll('a[href^="#"]').forEach(anchor => {
  anchor.addEventListener('click', function (e) {
    e.preventDefault();
    const target = document.querySelector(this.getAttribute('href'));
    if (target) {
      target.scrollIntoView({
        behavior: 'smooth',
        block: 'start'
      });
    }
  });
});

// Add back to top button functionality
function addBackToTopButton() {
  const backToTopBtn = document.createElement('button');
  backToTopBtn.innerHTML = '<i class="fas fa-arrow-up"></i>';
  backToTopBtn.className = 'btn btn-primary position-fixed';
  backToTopBtn.style.cssText = `
    bottom: 20px;
    left: 20px;
    z-index: 1000;
    border-radius: 50%;
    width: 50px;
    height: 50px;
    display: none;
  `;
  
  document.body.appendChild(backToTopBtn);

  // Show/hide button based on scroll position
  window.addEventListener('scroll', () => {
    if (window.pageYOffset > 300) {
      backToTopBtn.style.display = 'block';
    } else {
      backToTopBtn.style.display = 'none';
    }
  });

  // Scroll to top when clicked
  backToTopBtn.addEventListener('click', () => {
    Utils.scrollToTop();
  });
}

// Initialize back to top button
addBackToTopButton();
