const Category = require('../models/Category');
const { AppError, asyncHandler } = require('../middleware/errorHandler');

// @desc    Get all categories
// @route   GET /api/categories
// @access  Public
exports.getCategories = asyncHandler(async (req, res, next) => {
  // Copy req.query
  const reqQuery = { ...req.query };

  // Fields to exclude
  const removeFields = ['select', 'sort', 'page', 'limit', 'tree'];

  // Loop over removeFields and delete them from reqQuery
  removeFields.forEach(param => delete reqQuery[param]);

  // Add default filters
  if (!req.user || req.user.role !== 'admin') {
    reqQuery.status = 'active';
  }

  // Check if tree structure is requested
  if (req.query.tree === 'true') {
    const tree = await Category.getTree();
    return res.status(200).json({
      success: true,
      data: tree
    });
  }

  // Build query
  let query = Category.find(reqQuery);

  // Select Fields
  if (req.query.select) {
    const fields = req.query.select.split(',').join(' ');
    query = query.select(fields);
  }

  // Sort
  if (req.query.sort) {
    const sortBy = req.query.sort.split(',').join(' ');
    query = query.sort(sortBy);
  } else {
    query = query.sort('sortOrder name.ar');
  }

  // Pagination
  const page = parseInt(req.query.page, 10) || 1;
  const limit = parseInt(req.query.limit, 10) || 50;
  const startIndex = (page - 1) * limit;
  const endIndex = page * limit;
  const total = await Category.countDocuments(reqQuery);

  query = query.skip(startIndex).limit(limit);

  // Populate
  if (req.query.populate) {
    const populateFields = req.query.populate.split(',');
    populateFields.forEach(field => {
      if (field === 'parent') {
        query = query.populate('parent', 'name slug');
      } else if (field === 'children') {
        query = query.populate('children', 'name slug');
      }
    });
  }

  // Executing query
  const categories = await query;

  // Pagination result
  const pagination = {};

  if (endIndex < total) {
    pagination.next = {
      page: page + 1,
      limit
    };
  }

  if (startIndex > 0) {
    pagination.prev = {
      page: page - 1,
      limit
    };
  }

  res.status(200).json({
    success: true,
    count: categories.length,
    total,
    pagination,
    data: categories
  });
});

// @desc    Get single category
// @route   GET /api/categories/:id
// @access  Public
exports.getCategory = asyncHandler(async (req, res, next) => {
  const category = await Category.findById(req.params.id)
    .populate('parent', 'name slug')
    .populate('children', 'name slug image sortOrder');

  if (!category) {
    return next(new AppError(
      'الفئة غير موجودة',
      404,
      'Category not found'
    ));
  }

  // Check if category is active (unless admin)
  if (category.status !== 'active' && (!req.user || req.user.role !== 'admin')) {
    return next(new AppError(
      'الفئة غير متاحة',
      404,
      'Category not available'
    ));
  }

  // Get breadcrumb
  const breadcrumb = await category.getBreadcrumb();

  res.status(200).json({
    success: true,
    data: {
      ...category.toObject(),
      breadcrumb
    }
  });
});

// @desc    Create new category
// @route   POST /api/categories
// @access  Private (Admin/Manager)
exports.createCategory = asyncHandler(async (req, res, next) => {
  // Add user to req.body
  req.body.createdBy = req.user.id;

  // Check if parent exists
  if (req.body.parent) {
    const parent = await Category.findById(req.body.parent);
    if (!parent) {
      return next(new AppError(
        'الفئة الأب غير موجودة',
        404,
        'Parent category not found'
      ));
    }
  }

  // Handle uploaded image
  if (req.uploadedImage) {
    req.body.image = {
      url: req.uploadedImage.url,
      alt: {
        ar: req.body.name?.ar || 'صورة الفئة',
        en: req.body.name?.en || 'Category image'
      }
    };
  }

  const category = await Category.create(req.body);

  res.status(201).json({
    success: true,
    message: 'تم إنشاء الفئة بنجاح',
    messageEn: 'Category created successfully',
    data: category
  });
});

// @desc    Update category
// @route   PUT /api/categories/:id
// @access  Private (Admin/Manager)
exports.updateCategory = asyncHandler(async (req, res, next) => {
  let category = await Category.findById(req.params.id);

  if (!category) {
    return next(new AppError(
      'الفئة غير موجودة',
      404,
      'Category not found'
    ));
  }

  // Check if parent exists and is not the same category
  if (req.body.parent) {
    if (req.body.parent === req.params.id) {
      return next(new AppError(
        'الفئة لا يمكن أن تكون والد لنفسها',
        400,
        'Category cannot be parent of itself'
      ));
    }

    const parent = await Category.findById(req.body.parent);
    if (!parent) {
      return next(new AppError(
        'الفئة الأب غير موجودة',
        404,
        'Parent category not found'
      ));
    }
  }

  // Add user to req.body
  req.body.updatedBy = req.user.id;

  // Handle uploaded image
  if (req.uploadedImage) {
    req.body.image = {
      url: req.uploadedImage.url,
      alt: {
        ar: req.body.name?.ar || category.name.ar,
        en: req.body.name?.en || category.name.en
      }
    };
  }

  category = await Category.findByIdAndUpdate(req.params.id, req.body, {
    new: true,
    runValidators: true
  });

  res.status(200).json({
    success: true,
    message: 'تم تحديث الفئة بنجاح',
    messageEn: 'Category updated successfully',
    data: category
  });
});

// @desc    Delete category
// @route   DELETE /api/categories/:id
// @access  Private (Admin)
exports.deleteCategory = asyncHandler(async (req, res, next) => {
  const category = await Category.findById(req.params.id);

  if (!category) {
    return next(new AppError(
      'الفئة غير موجودة',
      404,
      'Category not found'
    ));
  }

  // Check if category has children
  const children = await Category.find({ parent: req.params.id });
  if (children.length > 0) {
    return next(new AppError(
      'لا يمكن حذف فئة تحتوي على فئات فرعية',
      400,
      'Cannot delete category with subcategories'
    ));
  }

  // Check if category has products
  const Product = require('../models/Product');
  const productCount = await Product.countDocuments({ category: req.params.id });
  if (productCount > 0) {
    return next(new AppError(
      'لا يمكن حذف فئة تحتوي على منتجات',
      400,
      'Cannot delete category with products'
    ));
  }

  await category.remove();

  res.status(200).json({
    success: true,
    message: 'تم حذف الفئة بنجاح',
    messageEn: 'Category deleted successfully'
  });
});

// @desc    Get category tree
// @route   GET /api/categories/tree
// @access  Public
exports.getCategoryTree = asyncHandler(async (req, res, next) => {
  const maxLevel = req.query.maxLevel ? parseInt(req.query.maxLevel) : null;
  const tree = await Category.getTree(null, maxLevel);

  res.status(200).json({
    success: true,
    data: tree
  });
});

// @desc    Get featured categories
// @route   GET /api/categories/featured
// @access  Public
exports.getFeaturedCategories = asyncHandler(async (req, res, next) => {
  const limit = parseInt(req.query.limit, 10) || 10;
  
  const categories = await Category.getFeatured(limit);

  res.status(200).json({
    success: true,
    count: categories.length,
    data: categories
  });
});

// @desc    Search categories
// @route   GET /api/categories/search
// @access  Public
exports.searchCategories = asyncHandler(async (req, res, next) => {
  const { q } = req.query;

  if (!q) {
    return next(new AppError(
      'كلمة البحث مطلوبة',
      400,
      'Search query is required'
    ));
  }

  const limit = parseInt(req.query.limit, 10) || 20;
  const categories = await Category.search(q, { limit });

  res.status(200).json({
    success: true,
    count: categories.length,
    searchQuery: q,
    data: categories
  });
});

// @desc    Get category path/breadcrumb
// @route   GET /api/categories/:id/path
// @access  Public
exports.getCategoryPath = asyncHandler(async (req, res, next) => {
  const category = await Category.findById(req.params.id);

  if (!category) {
    return next(new AppError(
      'الفئة غير موجودة',
      404,
      'Category not found'
    ));
  }

  const path = await Category.getPath(req.params.id);

  res.status(200).json({
    success: true,
    data: path
  });
});

// @desc    Update category sort order
// @route   PUT /api/categories/sort
// @access  Private (Admin/Manager)
exports.updateSortOrder = asyncHandler(async (req, res, next) => {
  const { categories } = req.body;

  if (!categories || !Array.isArray(categories)) {
    return next(new AppError(
      'قائمة الفئات مطلوبة',
      400,
      'Categories array is required'
    ));
  }

  // Update sort order for each category
  const updatePromises = categories.map(cat => 
    Category.findByIdAndUpdate(
      cat.id,
      { 
        sortOrder: cat.sortOrder,
        updatedBy: req.user.id
      },
      { new: true }
    )
  );

  await Promise.all(updatePromises);

  res.status(200).json({
    success: true,
    message: 'تم تحديث ترتيب الفئات بنجاح',
    messageEn: 'Category sort order updated successfully'
  });
});
