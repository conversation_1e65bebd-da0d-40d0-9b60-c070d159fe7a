const jwt = require('jsonwebtoken');
const User = require('../models/User');

// Protect routes - require authentication
exports.protect = async (req, res, next) => {
  let token;

  // Check for token in headers
  if (req.headers.authorization && req.headers.authorization.startsWith('Bearer')) {
    token = req.headers.authorization.split(' ')[1];
  }
  // Check for token in cookies
  else if (req.cookies.token) {
    token = req.cookies.token;
  }

  // Make sure token exists
  if (!token) {
    return res.status(401).json({
      success: false,
      message: 'غير مصرح لك بالوصول إلى هذا المورد',
      messageEn: 'Not authorized to access this resource'
    });
  }

  try {
    // Verify token
    const decoded = jwt.verify(token, process.env.JWT_SECRET);

    // Get user from token
    const user = await User.findById(decoded.id).select('+password');

    if (!user) {
      return res.status(401).json({
        success: false,
        message: 'المستخدم غير موجود',
        messageEn: 'User not found'
      });
    }

    // Check if user is active
    if (!user.isActive) {
      return res.status(401).json({
        success: false,
        message: 'تم إيقاف حسابك، يرجى التواصل مع الإدارة',
        messageEn: 'Your account has been deactivated'
      });
    }

    // Check if account is locked
    if (user.isLocked) {
      return res.status(401).json({
        success: false,
        message: 'تم قفل حسابك مؤقتاً بسبب محاولات دخول خاطئة متكررة',
        messageEn: 'Account temporarily locked due to too many failed login attempts'
      });
    }

    // Update last login
    user.lastLogin = new Date();
    await user.save({ validateBeforeSave: false });

    req.user = user;
    next();
  } catch (error) {
    console.error('Auth middleware error:', error);
    
    if (error.name === 'JsonWebTokenError') {
      return res.status(401).json({
        success: false,
        message: 'رمز المصادقة غير صحيح',
        messageEn: 'Invalid token'
      });
    } else if (error.name === 'TokenExpiredError') {
      return res.status(401).json({
        success: false,
        message: 'انتهت صلاحية رمز المصادقة',
        messageEn: 'Token expired'
      });
    }
    
    return res.status(401).json({
      success: false,
      message: 'غير مصرح لك بالوصول إلى هذا المورد',
      messageEn: 'Not authorized to access this resource'
    });
  }
};

// Grant access to specific roles
exports.authorize = (...roles) => {
  return (req, res, next) => {
    if (!req.user) {
      return res.status(401).json({
        success: false,
        message: 'يجب تسجيل الدخول أولاً',
        messageEn: 'Please login first'
      });
    }

    if (!roles.includes(req.user.role)) {
      return res.status(403).json({
        success: false,
        message: 'ليس لديك صلاحية للوصول إلى هذا المورد',
        messageEn: 'You do not have permission to access this resource'
      });
    }

    next();
  };
};

// Optional authentication - doesn't require login but adds user if logged in
exports.optionalAuth = async (req, res, next) => {
  let token;

  // Check for token in headers
  if (req.headers.authorization && req.headers.authorization.startsWith('Bearer')) {
    token = req.headers.authorization.split(' ')[1];
  }
  // Check for token in cookies
  else if (req.cookies.token) {
    token = req.cookies.token;
  }

  if (token) {
    try {
      // Verify token
      const decoded = jwt.verify(token, process.env.JWT_SECRET);

      // Get user from token
      const user = await User.findById(decoded.id);

      if (user && user.isActive && !user.isLocked) {
        req.user = user;
      }
    } catch (error) {
      // Token is invalid, but we don't throw error for optional auth
      console.log('Optional auth failed:', error.message);
    }
  }

  next();
};

// Check if user owns the resource
exports.checkOwnership = (resourceModel, resourceIdParam = 'id', userField = 'user') => {
  return async (req, res, next) => {
    try {
      const resourceId = req.params[resourceIdParam];
      const Model = require(`../models/${resourceModel}`);
      
      const resource = await Model.findById(resourceId);
      
      if (!resource) {
        return res.status(404).json({
          success: false,
          message: 'المورد غير موجود',
          messageEn: 'Resource not found'
        });
      }

      // Admin can access everything
      if (req.user.role === 'admin') {
        req.resource = resource;
        return next();
      }

      // Check ownership
      const resourceUserId = resource[userField];
      if (!resourceUserId || resourceUserId.toString() !== req.user._id.toString()) {
        return res.status(403).json({
          success: false,
          message: 'ليس لديك صلاحية للوصول إلى هذا المورد',
          messageEn: 'You do not have permission to access this resource'
        });
      }

      req.resource = resource;
      next();
    } catch (error) {
      console.error('Ownership check error:', error);
      return res.status(500).json({
        success: false,
        message: 'خطأ في الخادم',
        messageEn: 'Server error'
      });
    }
  };
};

// Rate limiting for sensitive operations
exports.sensitiveOperationLimit = (maxAttempts = 5, windowMs = 15 * 60 * 1000) => {
  const attempts = new Map();

  return (req, res, next) => {
    const key = req.ip + (req.user ? req.user._id : '');
    const now = Date.now();
    
    if (!attempts.has(key)) {
      attempts.set(key, { count: 1, resetTime: now + windowMs });
      return next();
    }

    const userAttempts = attempts.get(key);
    
    if (now > userAttempts.resetTime) {
      userAttempts.count = 1;
      userAttempts.resetTime = now + windowMs;
      return next();
    }

    if (userAttempts.count >= maxAttempts) {
      return res.status(429).json({
        success: false,
        message: 'تم تجاوز الحد المسموح من المحاولات، يرجى المحاولة لاحقاً',
        messageEn: 'Too many attempts, please try again later',
        retryAfter: Math.ceil((userAttempts.resetTime - now) / 1000)
      });
    }

    userAttempts.count++;
    next();
  };
};

// Verify email middleware
exports.requireEmailVerification = (req, res, next) => {
  if (!req.user.isEmailVerified) {
    return res.status(403).json({
      success: false,
      message: 'يجب تأكيد البريد الإلكتروني أولاً',
      messageEn: 'Email verification required',
      code: 'EMAIL_NOT_VERIFIED'
    });
  }
  next();
};

// Check if user can perform action based on subscription/plan
exports.checkUserLimits = (action) => {
  return async (req, res, next) => {
    // This can be extended based on user subscription plans
    // For now, we'll implement basic limits
    
    const limits = {
      'create_review': { maxPerDay: 10 },
      'create_order': { maxPerDay: 20 },
      'upload_image': { maxPerDay: 50 }
    };

    if (!limits[action]) {
      return next();
    }

    try {
      // Check daily limits (this would typically use Redis for better performance)
      const today = new Date();
      today.setHours(0, 0, 0, 0);
      
      // This is a simplified check - in production, you'd want to use Redis
      // or a more sophisticated rate limiting solution
      
      next();
    } catch (error) {
      console.error('User limits check error:', error);
      return res.status(500).json({
        success: false,
        message: 'خطأ في الخادم',
        messageEn: 'Server error'
      });
    }
  };
};

// Middleware to log user actions for audit trail
exports.logUserAction = (action) => {
  return (req, res, next) => {
    // Log user action for audit trail
    console.log(`User ${req.user ? req.user._id : 'anonymous'} performed action: ${action} at ${new Date()}`);
    
    // In production, you'd want to store this in a database or logging service
    req.userAction = action;
    next();
  };
};

module.exports = exports;
