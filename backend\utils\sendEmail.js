const nodemailer = require('nodemailer');

// Email templates
const emailTemplates = {
  emailVerification: {
    ar: {
      subject: 'تأكيد البريد الإلكتروني - {{storeName}}',
      html: `
        <div dir="rtl" style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto; padding: 20px;">
          <div style="text-align: center; margin-bottom: 30px;">
            <h1 style="color: #2c3e50;">{{storeName}}</h1>
          </div>
          
          <div style="background: #f8f9fa; padding: 30px; border-radius: 10px;">
            <h2 style="color: #2c3e50; margin-bottom: 20px;">مرحباً {{name}}!</h2>
            
            <p style="font-size: 16px; line-height: 1.6; color: #555;">
              شكراً لك على التسجيل في متجرنا. لإكمال عملية التسجيل، يرجى تأكيد بريدك الإلكتروني بالنقر على الرابط أدناه:
            </p>
            
            <div style="text-align: center; margin: 30px 0;">
              <a href="{{verificationUrl}}" 
                 style="background: #007bff; color: white; padding: 15px 30px; text-decoration: none; border-radius: 5px; display: inline-block; font-weight: bold;">
                تأكيد البريد الإلكتروني
              </a>
            </div>
            
            <p style="font-size: 14px; color: #666; margin-top: 20px;">
              إذا لم تقم بإنشاء حساب، يرجى تجاهل هذا البريد الإلكتروني.
            </p>
            
            <p style="font-size: 14px; color: #666;">
              هذا الرابط صالح لمدة 24 ساعة فقط.
            </p>
          </div>
          
          <div style="text-align: center; margin-top: 30px; font-size: 12px; color: #999;">
            <p>{{storeName}} - أفضل متجر إلكتروني</p>
          </div>
        </div>
      `
    },
    en: {
      subject: 'Email Verification - {{storeName}}',
      html: `
        <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto; padding: 20px;">
          <div style="text-align: center; margin-bottom: 30px;">
            <h1 style="color: #2c3e50;">{{storeName}}</h1>
          </div>
          
          <div style="background: #f8f9fa; padding: 30px; border-radius: 10px;">
            <h2 style="color: #2c3e50; margin-bottom: 20px;">Hello {{name}}!</h2>
            
            <p style="font-size: 16px; line-height: 1.6; color: #555;">
              Thank you for registering with our store. To complete your registration, please verify your email address by clicking the link below:
            </p>
            
            <div style="text-align: center; margin: 30px 0;">
              <a href="{{verificationUrl}}" 
                 style="background: #007bff; color: white; padding: 15px 30px; text-decoration: none; border-radius: 5px; display: inline-block; font-weight: bold;">
                Verify Email
              </a>
            </div>
            
            <p style="font-size: 14px; color: #666; margin-top: 20px;">
              If you didn't create an account, please ignore this email.
            </p>
            
            <p style="font-size: 14px; color: #666;">
              This link is valid for 24 hours only.
            </p>
          </div>
          
          <div style="text-align: center; margin-top: 30px; font-size: 12px; color: #999;">
            <p>{{storeName}} - Your Best Online Store</p>
          </div>
        </div>
      `
    }
  },
  
  passwordReset: {
    ar: {
      subject: 'استعادة كلمة المرور - {{storeName}}',
      html: `
        <div dir="rtl" style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto; padding: 20px;">
          <div style="text-align: center; margin-bottom: 30px;">
            <h1 style="color: #2c3e50;">{{storeName}}</h1>
          </div>
          
          <div style="background: #f8f9fa; padding: 30px; border-radius: 10px;">
            <h2 style="color: #2c3e50; margin-bottom: 20px;">مرحباً {{name}}!</h2>
            
            <p style="font-size: 16px; line-height: 1.6; color: #555;">
              تلقينا طلباً لاستعادة كلمة المرور لحسابك. لإنشاء كلمة مرور جديدة، انقر على الرابط أدناه:
            </p>
            
            <div style="text-align: center; margin: 30px 0;">
              <a href="{{resetUrl}}" 
                 style="background: #dc3545; color: white; padding: 15px 30px; text-decoration: none; border-radius: 5px; display: inline-block; font-weight: bold;">
                استعادة كلمة المرور
              </a>
            </div>
            
            <p style="font-size: 14px; color: #666; margin-top: 20px;">
              إذا لم تطلب استعادة كلمة المرور، يرجى تجاهل هذا البريد الإلكتروني.
            </p>
            
            <p style="font-size: 14px; color: #666;">
              هذا الرابط صالح لمدة 10 دقائق فقط.
            </p>
          </div>
          
          <div style="text-align: center; margin-top: 30px; font-size: 12px; color: #999;">
            <p>{{storeName}} - أفضل متجر إلكتروني</p>
          </div>
        </div>
      `
    },
    en: {
      subject: 'Password Reset - {{storeName}}',
      html: `
        <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto; padding: 20px;">
          <div style="text-align: center; margin-bottom: 30px;">
            <h1 style="color: #2c3e50;">{{storeName}}</h1>
          </div>
          
          <div style="background: #f8f9fa; padding: 30px; border-radius: 10px;">
            <h2 style="color: #2c3e50; margin-bottom: 20px;">Hello {{name}}!</h2>
            
            <p style="font-size: 16px; line-height: 1.6; color: #555;">
              We received a request to reset your password. To create a new password, click the link below:
            </p>
            
            <div style="text-align: center; margin: 30px 0;">
              <a href="{{resetUrl}}" 
                 style="background: #dc3545; color: white; padding: 15px 30px; text-decoration: none; border-radius: 5px; display: inline-block; font-weight: bold;">
                Reset Password
              </a>
            </div>
            
            <p style="font-size: 14px; color: #666; margin-top: 20px;">
              If you didn't request a password reset, please ignore this email.
            </p>
            
            <p style="font-size: 14px; color: #666;">
              This link is valid for 10 minutes only.
            </p>
          </div>
          
          <div style="text-align: center; margin-top: 30px; font-size: 12px; color: #999;">
            <p>{{storeName}} - Your Best Online Store</p>
          </div>
        </div>
      `
    }
  },
  
  orderConfirmation: {
    ar: {
      subject: 'تأكيد الطلب #{{orderNumber}} - {{storeName}}',
      html: `
        <div dir="rtl" style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto; padding: 20px;">
          <div style="text-align: center; margin-bottom: 30px;">
            <h1 style="color: #2c3e50;">{{storeName}}</h1>
          </div>
          
          <div style="background: #f8f9fa; padding: 30px; border-radius: 10px;">
            <h2 style="color: #2c3e50; margin-bottom: 20px;">شكراً لك {{name}}!</h2>
            
            <p style="font-size: 16px; line-height: 1.6; color: #555;">
              تم استلام طلبك بنجاح. إليك تفاصيل طلبك:
            </p>
            
            <div style="background: white; padding: 20px; border-radius: 5px; margin: 20px 0;">
              <h3 style="color: #2c3e50; margin-bottom: 15px;">رقم الطلب: #{{orderNumber}}</h3>
              <p><strong>تاريخ الطلب:</strong> {{orderDate}}</p>
              <p><strong>المجموع:</strong> {{total}} {{currency}}</p>
              <p><strong>طريقة الدفع:</strong> {{paymentMethod}}</p>
            </div>
            
            <p style="font-size: 14px; color: #666;">
              سنقوم بإرسال تحديثات حول حالة طلبك عبر البريد الإلكتروني.
            </p>
          </div>
          
          <div style="text-align: center; margin-top: 30px; font-size: 12px; color: #999;">
            <p>{{storeName}} - أفضل متجر إلكتروني</p>
          </div>
        </div>
      `
    }
  }
};

// Replace template variables
const replaceTemplateVariables = (template, data) => {
  let result = template;
  Object.keys(data).forEach(key => {
    const regex = new RegExp(`{{${key}}}`, 'g');
    result = result.replace(regex, data[key] || '');
  });
  return result;
};

// Create transporter
const createTransporter = () => {
  return nodemailer.createTransporter({
    host: process.env.EMAIL_HOST,
    port: process.env.EMAIL_PORT,
    secure: process.env.EMAIL_PORT == 465, // true for 465, false for other ports
    auth: {
      user: process.env.EMAIL_USER,
      pass: process.env.EMAIL_PASS
    }
  });
};

// Send email function
const sendEmail = async (options) => {
  try {
    const transporter = createTransporter();
    
    let { email, subject, message, html, template, data = {} } = options;
    
    // Add default data
    data.storeName = data.storeName || process.env.STORE_NAME || 'متجرنا الإلكتروني';
    
    // Use template if provided
    if (template && emailTemplates[template]) {
      const language = data.language || 'ar';
      const templateData = emailTemplates[template][language];
      
      if (templateData) {
        subject = replaceTemplateVariables(templateData.subject, data);
        html = replaceTemplateVariables(templateData.html, data);
      }
    }
    
    // Fallback to plain text if no HTML
    if (!html && message) {
      html = `<div style="font-family: Arial, sans-serif; padding: 20px;">${message}</div>`;
    }
    
    const mailOptions = {
      from: `${data.storeName} <${process.env.EMAIL_FROM}>`,
      to: email,
      subject,
      html
    };
    
    const info = await transporter.sendMail(mailOptions);
    
    console.log('Email sent successfully:', info.messageId);
    return info;
    
  } catch (error) {
    console.error('Email sending failed:', error);
    throw error;
  }
};

// Send bulk emails
const sendBulkEmail = async (emails, options) => {
  const results = [];
  
  for (const email of emails) {
    try {
      const result = await sendEmail({
        ...options,
        email
      });
      results.push({ email, success: true, messageId: result.messageId });
    } catch (error) {
      results.push({ email, success: false, error: error.message });
    }
  }
  
  return results;
};

// Test email configuration
const testEmailConfig = async () => {
  try {
    const transporter = createTransporter();
    await transporter.verify();
    console.log('✅ Email configuration is valid');
    return true;
  } catch (error) {
    console.error('❌ Email configuration error:', error);
    return false;
  }
};

module.exports = {
  sendEmail,
  sendBulkEmail,
  testEmailConfig,
  emailTemplates
};
