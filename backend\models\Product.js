const mongoose = require('mongoose');
const slugify = require('slugify');

const productSchema = new mongoose.Schema({
  name: {
    ar: {
      type: String,
      required: [true, 'اسم المنتج باللغة العربية مطلوب'],
      trim: true,
      maxlength: [100, 'اسم المنتج يجب أن يكون أقل من 100 حرف']
    },
    en: {
      type: String,
      trim: true,
      maxlength: [100, 'Product name must be less than 100 characters']
    }
  },
  slug: {
    type: String,
    unique: true
  },
  description: {
    ar: {
      type: String,
      required: [true, 'وصف المنتج باللغة العربية مطلوب'],
      maxlength: [2000, 'الوصف يجب أن يكون أقل من 2000 حرف']
    },
    en: {
      type: String,
      maxlength: [2000, 'Description must be less than 2000 characters']
    }
  },
  shortDescription: {
    ar: {
      type: String,
      maxlength: [200, 'الوصف المختصر يجب أن يكون أقل من 200 حرف']
    },
    en: {
      type: String,
      maxlength: [200, 'Short description must be less than 200 characters']
    }
  },
  price: {
    type: Number,
    required: [true, 'سعر المنتج مطلوب'],
    min: [0, 'السعر لا يمكن أن يكون سالباً']
  },
  comparePrice: {
    type: Number,
    min: [0, 'سعر المقارنة لا يمكن أن يكون سالباً']
  },
  cost: {
    type: Number,
    min: [0, 'التكلفة لا يمكن أن تكون سالبة']
  },
  sku: {
    type: String,
    unique: true,
    sparse: true,
    uppercase: true
  },
  barcode: {
    type: String,
    unique: true,
    sparse: true
  },
  category: {
    type: mongoose.Schema.ObjectId,
    ref: 'Category',
    required: [true, 'فئة المنتج مطلوبة']
  },
  subcategory: {
    type: mongoose.Schema.ObjectId,
    ref: 'Category'
  },
  brand: {
    type: String,
    trim: true
  },
  images: [{
    url: {
      type: String,
      required: true
    },
    alt: {
      ar: String,
      en: String
    },
    isMain: {
      type: Boolean,
      default: false
    }
  }],
  variants: [{
    name: {
      ar: String,
      en: String
    },
    options: [{
      value: {
        ar: String,
        en: String
      },
      price: Number,
      sku: String,
      stock: {
        type: Number,
        default: 0
      },
      image: String
    }]
  }],
  attributes: [{
    name: {
      ar: String,
      en: String
    },
    value: {
      ar: String,
      en: String
    }
  }],
  stock: {
    quantity: {
      type: Number,
      required: [true, 'كمية المخزون مطلوبة'],
      min: [0, 'الكمية لا يمكن أن تكون سالبة'],
      default: 0
    },
    reserved: {
      type: Number,
      default: 0,
      min: [0, 'الكمية المحجوزة لا يمكن أن تكون سالبة']
    },
    lowStockThreshold: {
      type: Number,
      default: 5,
      min: [0, 'حد المخزون المنخفض لا يمكن أن يكون سالباً']
    },
    trackQuantity: {
      type: Boolean,
      default: true
    }
  },
  shipping: {
    weight: {
      type: Number,
      min: [0, 'الوزن لا يمكن أن يكون سالباً']
    },
    dimensions: {
      length: Number,
      width: Number,
      height: Number
    },
    shippingClass: {
      type: String,
      enum: ['standard', 'express', 'free', 'heavy'],
      default: 'standard'
    },
    freeShipping: {
      type: Boolean,
      default: false
    }
  },
  seo: {
    metaTitle: {
      ar: String,
      en: String
    },
    metaDescription: {
      ar: String,
      en: String
    },
    keywords: [String]
  },
  status: {
    type: String,
    enum: ['active', 'inactive', 'draft', 'archived'],
    default: 'active'
  },
  featured: {
    type: Boolean,
    default: false
  },
  tags: [String],
  rating: {
    average: {
      type: Number,
      default: 0,
      min: [0, 'التقييم لا يمكن أن يكون سالباً'],
      max: [5, 'التقييم لا يمكن أن يكون أكثر من 5']
    },
    count: {
      type: Number,
      default: 0
    }
  },
  reviews: [{
    type: mongoose.Schema.ObjectId,
    ref: 'Review'
  }],
  sales: {
    totalSold: {
      type: Number,
      default: 0
    },
    revenue: {
      type: Number,
      default: 0
    }
  },
  visibility: {
    type: String,
    enum: ['public', 'private', 'password'],
    default: 'public'
  },
  publishedAt: Date,
  createdBy: {
    type: mongoose.Schema.ObjectId,
    ref: 'User'
  },
  updatedBy: {
    type: mongoose.Schema.ObjectId,
    ref: 'User'
  }
}, {
  timestamps: true,
  toJSON: { virtuals: true },
  toObject: { virtuals: true }
});

// Virtual for available stock
productSchema.virtual('availableStock').get(function() {
  return this.stock.quantity - this.stock.reserved;
});

// Virtual for discount percentage
productSchema.virtual('discountPercentage').get(function() {
  if (this.comparePrice && this.comparePrice > this.price) {
    return Math.round(((this.comparePrice - this.price) / this.comparePrice) * 100);
  }
  return 0;
});

// Virtual for low stock status
productSchema.virtual('isLowStock').get(function() {
  return this.availableStock <= this.stock.lowStockThreshold;
});

// Virtual for out of stock status
productSchema.virtual('isOutOfStock').get(function() {
  return this.availableStock <= 0;
});

// Indexes for better performance
productSchema.index({ slug: 1 });
productSchema.index({ category: 1 });
productSchema.index({ status: 1 });
productSchema.index({ featured: 1 });
productSchema.index({ 'name.ar': 'text', 'name.en': 'text', 'description.ar': 'text', 'description.en': 'text' });
productSchema.index({ price: 1 });
productSchema.index({ 'rating.average': -1 });
productSchema.index({ createdAt: -1 });
productSchema.index({ 'sales.totalSold': -1 });

// Generate slug before saving
productSchema.pre('save', function(next) {
  if (this.isModified('name.ar') || this.isModified('name.en') || !this.slug) {
    const nameForSlug = this.name.en || this.name.ar;
    this.slug = slugify(nameForSlug, {
      lower: true,
      strict: true,
      locale: 'ar'
    });
  }
  next();
});

// Update published date when status changes to active
productSchema.pre('save', function(next) {
  if (this.isModified('status') && this.status === 'active' && !this.publishedAt) {
    this.publishedAt = new Date();
  }
  next();
});

// Static method to get products by category
productSchema.statics.getByCategory = function(categoryId, options = {}) {
  const query = { category: categoryId, status: 'active' };
  return this.find(query)
    .populate('category', 'name slug')
    .sort(options.sort || { createdAt: -1 })
    .limit(options.limit || 20);
};

// Static method to get featured products
productSchema.statics.getFeatured = function(limit = 10) {
  return this.find({ featured: true, status: 'active' })
    .populate('category', 'name slug')
    .sort({ 'rating.average': -1, 'sales.totalSold': -1 })
    .limit(limit);
};

// Static method to search products
productSchema.statics.search = function(query, options = {}) {
  const searchQuery = {
    $text: { $search: query },
    status: 'active'
  };
  
  return this.find(searchQuery, { score: { $meta: 'textScore' } })
    .populate('category', 'name slug')
    .sort({ score: { $meta: 'textScore' } })
    .limit(options.limit || 20);
};

// Method to update rating
productSchema.methods.updateRating = async function() {
  const Review = mongoose.model('Review');
  const stats = await Review.aggregate([
    { $match: { product: this._id, isApproved: true } },
    {
      $group: {
        _id: '$product',
        averageRating: { $avg: '$rating' },
        totalReviews: { $sum: 1 }
      }
    }
  ]);
  
  if (stats.length > 0) {
    this.rating.average = Math.round(stats[0].averageRating * 10) / 10;
    this.rating.count = stats[0].totalReviews;
  } else {
    this.rating.average = 0;
    this.rating.count = 0;
  }
  
  await this.save();
};

module.exports = mongoose.model('Product', productSchema);
