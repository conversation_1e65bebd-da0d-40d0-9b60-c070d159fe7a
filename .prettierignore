# Dependencies
node_modules/

# Build outputs
dist/
build/
public/css/style.css
public/js/bundle.js

# Uploads
public/uploads/

# Logs
*.log
logs/

# Environment files
.env
.env.*

# Database files
*.db
*.sqlite

# Compressed files
*.zip
*.tar.gz
*.rar

# IDE files
.vscode/
.idea/

# OS files
.DS_Store
Thumbs.db

# Package files
package-lock.json
yarn.lock

# Coverage
coverage/

# Documentation build
docs/_build/
