/* Admin Panel Styles */

/* Admin Body */
.admin-body {
  background-color: #f8f9fa;
  font-family: 'Cairo', sans-serif;
}

/* Admin Navigation */
.navbar-dark {
  background: linear-gradient(135deg, #343a40 0%, #495057 100%) !important;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

.navbar-brand {
  font-size: 1.5rem;
  font-weight: 700;
}

.navbar-nav .nav-link {
  font-weight: 500;
  padding: 0.75rem 1rem;
  border-radius: 6px;
  margin: 0 0.25rem;
  transition: all 0.3s ease;
}

.navbar-nav .nav-link:hover {
  background-color: rgba(255, 255, 255, 0.1);
  transform: translateY(-1px);
}

.navbar-nav .nav-link.active {
  background-color: rgba(255, 255, 255, 0.2);
  font-weight: 600;
}

/* Cards */
.card {
  border-radius: 12px;
  transition: all 0.3s ease;
}

.card:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15) !important;
}

.card-header {
  border-radius: 12px 12px 0 0 !important;
  border-bottom: 1px solid #e9ecef;
  font-weight: 600;
}

/* Statistics Cards */
.card .bg-primary {
  background: linear-gradient(135deg, #007bff 0%, #0056b3 100%) !important;
}

.card .bg-success {
  background: linear-gradient(135deg, #28a745 0%, #1e7e34 100%) !important;
}

.card .bg-info {
  background: linear-gradient(135deg, #17a2b8 0%, #117a8b 100%) !important;
}

.card .bg-warning {
  background: linear-gradient(135deg, #ffc107 0%, #e0a800 100%) !important;
}

.rounded-circle {
  width: 60px;
  height: 60px;
  display: flex;
  align-items: center;
  justify-content: center;
}

/* Tables */
.table {
  border-radius: 8px;
  overflow: hidden;
}

.table th {
  background-color: #f8f9fa;
  border-bottom: 2px solid #dee2e6;
  font-weight: 600;
  color: #495057;
  padding: 1rem 0.75rem;
}

.table td {
  padding: 1rem 0.75rem;
  vertical-align: middle;
}

.table-hover tbody tr:hover {
  background-color: rgba(0, 123, 255, 0.05);
}

/* Badges */
.badge {
  font-size: 0.75rem;
  font-weight: 500;
  padding: 0.5rem 0.75rem;
  border-radius: 6px;
}

.badge.bg-success {
  background: linear-gradient(135deg, #28a745 0%, #20c997 100%) !important;
}

.badge.bg-warning {
  background: linear-gradient(135deg, #ffc107 0%, #fd7e14 100%) !important;
  color: #212529 !important;
}

.badge.bg-danger {
  background: linear-gradient(135deg, #dc3545 0%, #e83e8c 100%) !important;
}

.badge.bg-info {
  background: linear-gradient(135deg, #17a2b8 0%, #6f42c1 100%) !important;
}

/* Buttons */
.btn {
  border-radius: 8px;
  font-weight: 500;
  padding: 0.75rem 1.5rem;
  transition: all 0.3s ease;
  border: none;
}

.btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
}

.btn-primary {
  background: linear-gradient(135deg, #007bff 0%, #0056b3 100%);
}

.btn-success {
  background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
}

.btn-warning {
  background: linear-gradient(135deg, #ffc107 0%, #fd7e14 100%);
  color: #212529;
}

.btn-info {
  background: linear-gradient(135deg, #17a2b8 0%, #6f42c1 100%);
}

.btn-secondary {
  background: linear-gradient(135deg, #6c757d 0%, #495057 100%);
}

.btn-outline-primary {
  border: 2px solid #007bff;
  color: #007bff;
  background: transparent;
}

.btn-outline-primary:hover {
  background: #007bff;
  border-color: #007bff;
  color: white;
}

/* Forms */
.form-control {
  border-radius: 8px;
  border: 2px solid #e9ecef;
  padding: 0.75rem 1rem;
  transition: all 0.3s ease;
}

.form-control:focus {
  border-color: #007bff;
  box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
}

.form-select {
  border-radius: 8px;
  border: 2px solid #e9ecef;
  padding: 0.75rem 1rem;
}

.form-label {
  font-weight: 600;
  color: #495057;
  margin-bottom: 0.5rem;
}

/* Charts */
canvas {
  max-height: 300px !important;
}

/* Responsive Design */
@media (max-width: 768px) {
  .container-fluid {
    padding-left: 1rem;
    padding-right: 1rem;
  }
  
  .card-body {
    padding: 1rem;
  }
  
  .table-responsive {
    font-size: 0.875rem;
  }
  
  .btn {
    padding: 0.5rem 1rem;
    font-size: 0.875rem;
  }
  
  .h4 {
    font-size: 1.25rem;
  }
}

@media (max-width: 576px) {
  .navbar-brand {
    font-size: 1.25rem;
  }
  
  .card .rounded-circle {
    width: 50px;
    height: 50px;
  }
  
  .card .rounded-circle i {
    font-size: 1rem !important;
  }
  
  .h4 {
    font-size: 1.125rem;
  }
  
  .small {
    font-size: 0.75rem;
  }
}

/* Animation Classes */
.fade-in-up {
  opacity: 0;
  transform: translateY(20px);
  transition: opacity 0.6s ease, transform 0.6s ease;
}

.fade-in-up.visible {
  opacity: 1;
  transform: translateY(0);
}

.slide-in-right {
  opacity: 0;
  transform: translateX(30px);
  transition: opacity 0.6s ease, transform 0.6s ease;
}

.slide-in-right.visible {
  opacity: 1;
  transform: translateX(0);
}

/* Loading States */
.loading-skeleton {
  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
  background-size: 200% 100%;
  animation: loading 1.5s infinite;
}

@keyframes loading {
  0% {
    background-position: 200% 0;
  }
  100% {
    background-position: -200% 0;
  }
}

/* Status Indicators */
.status-online {
  position: relative;
}

.status-online::after {
  content: '';
  position: absolute;
  top: 0;
  right: 0;
  width: 12px;
  height: 12px;
  background: #28a745;
  border: 2px solid white;
  border-radius: 50%;
}

/* Custom Scrollbar */
::-webkit-scrollbar {
  width: 8px;
}

::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 4px;
}

::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}

/* Dark Mode Support */
@media (prefers-color-scheme: dark) {
  .admin-body {
    background-color: #1a1a1a;
    color: #ffffff;
  }
  
  .card {
    background-color: #2d2d2d;
    border-color: #404040;
  }
  
  .table {
    color: #ffffff;
  }
  
  .table th {
    background-color: #404040;
    color: #ffffff;
  }
  
  .form-control {
    background-color: #2d2d2d;
    border-color: #404040;
    color: #ffffff;
  }
  
  .form-control:focus {
    background-color: #2d2d2d;
    border-color: #007bff;
    color: #ffffff;
  }
}
